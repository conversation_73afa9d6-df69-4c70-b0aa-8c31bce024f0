import { IUserClaims } from 'SharedModules.Library';
import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, computed, EventEmitter, inject, Output, signal } from '@angular/core';
import { AbstractControl, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';
import { ButtonModule } from 'primeng/button';
import { InputTextModule } from 'primeng/inputtext';
import { CustomValidators } from '@platform.src/app/core/helpers/custom-validators';
import { AuthStateService } from 'SharedModules.Library';
import { RegisterService } from '@platform.src/app/core/services//register.service';
import { FormFieldValidationMessageComponent } from 'SharedModules.Library';
import { PrimeReactiveFormInputComponent } from 'SharedModules.Library';
import { trigger, state, style, transition, animate } from '@angular/animations';
import { StudentRegistrationProgressStepsComponent } from '../student-registration-progress-steps/student-registration-progress-steps.component';

@Component({
  selector: 'app-student-name-step-form',
  imports: [
    CommonModule,
    ButtonModule,
    InputTextModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule,
    FormFieldValidationMessageComponent,
    PrimeReactiveFormInputComponent,
    StudentRegistrationProgressStepsComponent,
  ],
  templateUrl: './student-name-step-form.component.html',
  styleUrl: './student-name-step-form.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: [
    trigger('slideAnimation', [
      // Initial state (visible)
      state('visible', style({
        transform: 'translateX(0)',
        opacity: 1
      })),
      // Slide out to left state
      state('slideOutLeft', style({
        transform: 'translateX(-100%)',
        opacity: 0
      })),
      // Transition from visible to slideOutLeft
      transition('visible => slideOutLeft', [
        animate('300ms ease-out')
      ])
    ])
  ]
})
export class StudentNameStepFormComponent {
  authService = inject(AuthStateService);
  user = computed(() => this.authService.getUserClaims());
  studentNameForm: FormGroup = new FormGroup({});
  @Output() submitStudentNameFormButtonClick = new EventEmitter<any>();
  submitted = signal(false);
  animationState = signal('visible');

  constructor(private formBuilder: FormBuilder, private registerService: RegisterService) {
  }

  ngOnInit(): void {
    console.log(this.registerService.getRegisterStudentRequest());
    this.studentNameForm = this.formBuilder.group({
      firstName: [this.registerService.getRegisterStudentRequest().firstName, (control: AbstractControl) => CustomValidators.nameValidator(control, 'First name')],
      lastName: [this.registerService.getRegisterStudentRequest().lastName, (control: AbstractControl) => CustomValidators.nameValidator(control, 'Last name')],
    });
  }

  submitStudentNameClicked(event: Event) {
    console.log(this.studentNameForm);
    // Mark all form controls as touched or dirty
    Object.values(this.studentNameForm.controls).forEach(control => {
      control.markAsTouched();
      control.markAsDirty();
    });
    this.submitted.set(true);
    if (this.studentNameForm.valid) {
      // Start the slide out animation
      this.animationState.set('slideOutLeft');
      const studentFirstName = this.studentNameForm.get('firstName')!.value;
      const studentLastName = this.studentNameForm.get('lastName')!.value;

      this.registerService.updateRegisterStudentRequest({ firstName: studentFirstName, lastName: studentLastName });
      if (this.user) {
        this.registerService.updateRegisterStudentRequest({
          ...this.registerService.getRegisterStudentRequest(),
          parentId: this.user().userId,
        });
      }
      console.log(this.registerService.getRegisterStudentRequest());
      
      // Wait for animation to complete before navigating
      setTimeout(() => {
        this.registerService.navigateToNextStep('student-info');
      }, 100); // Match this with animation duration
    }
  }
}
