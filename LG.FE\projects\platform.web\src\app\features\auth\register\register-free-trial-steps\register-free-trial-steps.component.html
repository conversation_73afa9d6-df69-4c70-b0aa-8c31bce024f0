<!--
    <app-otp-input (otpCompleted)="onOtpCompleted($event)"
        (otpResendRequested)="onOtpResendRequested($event)">
    </app-otp-input> -->
@defer (on immediate) {

{{isAnotherFreeTrialPage()}}
<!-- 
  @let title = isAnotherFreeTrialPage() ? 'Request a new Free Trial' : 'Create New Student';
  @let paragraphText = isAnotherFreeTrialPage() ? 'Fill in the details below to request a new free trial for your students' : 'Fill in the details below to get started with your student';

<div class="text-center mb-3 m-auto md:max-w-30rem mt-4">
  <div class="primary-purple-color fs-25 font-semibold mb-2">
    {{title}}
  </div>
  <span class="primary-purple-color font-medium fs-16 sm:text-base">
    {{paragraphText}}
  </span>
</div> -->


<app-card-split-layout [maxWidth]="'600px'" containerClass="p-0" [showRightSide]="false"
  [rightBgImage]="'/assets/images/graphic/formify-bg-21.svg'">
  <div left>
    <div class="flex flex-column gap-4 align-items-center justify-content-center h-full overflow-hidden relative z-3 
              ">


      <div
        class="w-full flex flex-column align-items-center justify-content-center z-3 p-3 registerTrialSteps__container"
        [appLoader]="generalService.divLoading()">


        <div class="sm:px-3 w-full" style="border-radius:53px">


          <div
            *ngIf="registerService.getRouteRegisterStepNumber(router.url) !== 7 && registerService.getRouteRegisterStepNumber(router.url) !== 2">


            <!-- <div class="primary-purple-color text-xl font-semibold text-center mb-3">
              {{ registerService.getTitleAndPrevRoute(router.url).title }}
            </div> -->
          </div>

          @if (!generalService.isObjectEmpty(studentRegistrationConfig)) {

            <!-- {{studentRegistrationConfig | json}} -->
          <app-dynamic-progress-steps [stepDefinitions]="studentRegistrationSteps"
            [baseRoutePattern]="studentRegistrationConfig!.baseRoutePattern"
            [flowTitle]="studentRegistrationConfig.flowTitle"
            [completionRoute]="studentRegistrationConfig.completionRoute"
            [disableNavigationOnCompletion]="studentRegistrationConfig.disableNavigationOnCompletion"
            [navigationCallback]="handleStepNavigation">
          </app-dynamic-progress-steps>
          }

          <router-outlet></router-outlet>
          @if (registerService.getTitleAndPrevRoute(router.url).prevRoute) {
          <div class="flex flex-row align-items-center justify-content-center gap-3 mt-4">
            <p-button [rounded]="true" (click)="goBackRoute()" class="" icon="pi pi-chevron-left" size="small"
              [link]="true" label="Back"
              styleClass="bg-gray-100 text-gray-600 hover:bg-gray-200 border-0 text-sm px-3 py-1"></p-button>
          </div>
          }

        </div>
      </div>
    </div>

  </div>

</app-card-split-layout>


} @placeholder {
<span>loading</span>
}