

<form (ngSubmit)="submitInfoClicked($event)" [formGroup]="form">

  <div class="flex align-items-center justify-content-between mb-4">
    <div class="flex align-items-center justify-content-start gap-4 w-full">
      <div class="flex flex-wrap justify-content-start gap-4 primary-purple-color">
        <div class="flex align-items-center">
          <p-radioButton [value]="IGenderEnum.Male" formControlName="gender" inputId="gender1" name="gender"
            styleClass="typeRadio"></p-radioButton>
          <label class="ml-2" for="gender1">Boy</label>
        </div>

        <div class="flex align-items-center">
          <p-radioButton [value]="IGenderEnum.Female" formControlName="gender" inputId="gender2" name="gender"
            styleClass="typeRadio"></p-radioButton>
          <label class="ml-2" for="gender2">Girl</label>
        </div>
      </div>
    </div>
  </div>
  <div class="w-full">

    <div class="w-full">

      <!-- {{form.controls['dateOfBirth'].value | json}} -->
      <lib-date-of-birth-picker
      [label]="'Your Student\'s age (2-17)'"
      [required]="true"
      [defaultDateYearsOffset]="7"
      [maxAge]="17"
      [formControlName]="'dateOfBirth'"
      [parentFormGroup]="form"
      [dateFormatToSubmit]="dateFormatToSubmit"
      [placeholder]="'dd/mm/yy'"
      (dateSelected)="onSelectedDateOfBirth($event)"
  ></lib-date-of-birth-picker>
      <!-- <p-datepicker #calendar (onMonthChange)="onSelectedMonth($event)" (onSelect)="onSelectedDateOfBirth($event)"
        size="small" (onShow)="onCalendarShow($event)" (onYearChange)="onSelectedYear($event)" [fluid]="true"
        [defaultDate]="defaultDate" [iconDisplay]="'input'" [inputStyleClass]="'w-full '" [maxDate]="maxDate"
        [maxlength]="10" [minDate]="minDate" [monthNavigator]="true"
        [panelStyleClass]="disabledNext() ? 'disable-datepicker-next' : '' + (disabledPrev() ? ' disable-datepicker-prev' : '')"
        [placeholder]="'dd/mm/yyyy'" [shortYearCutoff]="+1" [showIcon]="true" [yearNavigator]="true" appendTo="body"
        dateFormat="dd/mm/yy" formControlName="dateOfBirth" styleClass="w-full ">
      </p-datepicker> -->
    </div>
  </div>
  <app-form-field-validation-message [control]="form.controls['dateOfBirth']" messageClass="text-red-500"
    propertyName="Date of Birth"></app-form-field-validation-message>


  <label [class]="labelClass" for="p-calendar" [innerHTML]="generalService.getDisplayLabel('Your
  Student\'s native language', true )"></label>
  <p-dropdown [filter]="true" [options]="languages" [virtualScrollItemSize]="28" [virtualScroll]="true" appendTo="body"
    formControlName="nativeLanguage" placeholder="Select native language" styleClass="w-full full-width">
  </p-dropdown>
  <app-form-field-validation-message [control]="form.controls['nativeLanguage']" messageClass="text-red-500"
    propertyName="Native language"></app-form-field-validation-message>

  <label [class]="labelClass" for="p-calendar" [innerHTML]="generalService.getDisplayLabel('Your
  Student\'s language of interest', true )"></label>
  <p-dropdown (onChange)="onLanguageOfInterestChange($event)" [filter]="true" [options]="mltLanguages"
    [virtualScrollItemSize]="28" [virtualScroll]="true" appendTo="body" dataKey="id" filterBy="name"
    formControlName="languageOfInterestId" optionLabel="name" placeholder="Select language of interest"
    styleClass="w-full full-width">
  </p-dropdown>
  <app-form-field-validation-message [control]="form.controls['languageOfInterestId']" messageClass="text-red-500"
    propertyName="Language of interest"></app-form-field-validation-message>

  <label [class]="labelClass" for="p-calendar" [innerHTML]="generalService.getDisplayLabel('Your
  Student\'s level', true )"></label>
  <p-dropdown [filter]="false" [options]="levels" [virtualScrollItemSize]="28" [virtualScroll]="true" appendTo="body"
    dataKey="value" formControlName="level" optionValue="value" placeholder="Level" styleClass="w-full full-width">
  </p-dropdown>
  <app-form-field-validation-message [control]="form.controls['level']" messageClass="text-red-500"
    propertyName="Level"></app-form-field-validation-message>

  <div *ngIf="form.invalid && form.touched && formSubmitted" class="error-message">
    <p-message severity="danger" styleClass="w-full" text="Please fill in all required fields"></p-message>
  </div>
  <div class="text-center mt-4">

    <p-button role="button" type="submit" class=" gradient-purple-btn" styleClass="w-full" label="Save & Next"
      icon="pi pi-chevron-right" iconPos="right"></p-button>

  </div>

</form>