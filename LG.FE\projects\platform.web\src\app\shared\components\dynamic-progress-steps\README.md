# Dynamic Progress Steps Component

A reusable, route-based progress steps component that automatically configures itself based on the current route.

## Features

- **Automatic Route Detection**: Automatically detects which step configuration to use based on the current route
- **Route-to-Config Mapping**: Centralized mapping system that associates route patterns with step configurations
- **DRY Implementation**: No need to manually pass configurations - the component self-configures
- **Fallback Strategy**: Graceful handling when no matching route pattern is found
- **Enterprise-grade UI/UX**: Modern, responsive design with established color palette
- **Accessibility**: WCAG 2.1 AA compliant with proper focus states and reduced motion support

## Basic Usage

### Simple Auto-Configuration (Recommended)

```html
<!-- The component automatically detects the appropriate configuration based on current route -->
<app-dynamic-progress-steps></app-dynamic-progress-steps>
```

### Manual Configuration (Optional)

```html
<!-- You can still manually override the configuration if needed -->
<app-dynamic-progress-steps 
  [stepDefinitions]="customSteps"
  [flowTitle]="'Custom Flow'"
  [completionRoute]="'custom-complete'"
  [autoConfigureFromRoute]="false">
</app-dynamic-progress-steps>
```

## Supported Route Patterns

The component automatically recognizes these route patterns:

### Student Registration Flow (6 steps)
- Routes: `/student-name`, `/student-info`, `/student-availability`, `/student-more-details`, `/student-set-password`, `/register-success`
- Title: "Student Registration"
- Completion: `register-success`

### Trial Request Add Language Flow (2 steps)
- Routes: `/free-trial-reason`, `/trial-request-add-new-language`, `/free-trial-thank-you`
- Title: "Add New Language"
- Completion: `free-trial-thank-you`

### Generic Free Trial Flow (fallback)
- Routes: `/request-free-trial`
- Title: "Free Trial Request"
- Completion: `free-trial-thank-you`

## Adding New Route Configurations

To add support for a new multi-step flow, update the `ProgressStepsRouteConfigService`:

```typescript
// In progress-steps-route-config.service.ts
{
  patterns: [
    '/your-new-step-1',
    '/your-new-step-2',
    '/your-completion-step'
  ],
  config: {
    steps: [
      {
        id: 1,
        route: 'your-new-step-1',
        title: 'First Step',
        shortTitle: 'Step 1',
        description: 'Description of first step',
        icon: 'pi pi-user'
      },
      // ... more steps
    ],
    baseRoutePattern: '/your-base-route',
    flowTitle: 'Your Flow Title',
    completionRoute: 'your-completion-step',
    disableNavigationOnCompletion: true
  },
  priority: 80 // Higher priority = checked first
}
```

## Component Inputs

| Input | Type | Default | Description |
|-------|------|---------|-------------|
| `stepDefinitions` | `StepDefinition[]` | `undefined` | Manual step definitions (overrides auto-config) |
| `baseRoutePattern` | `string` | `undefined` | Base route pattern for matching |
| `flowTitle` | `string` | `undefined` | Title displayed in progress header |
| `completionRoute` | `string` | `undefined` | Route that represents completion |
| `navigationCallback` | `NavigationCallback` | `undefined` | Custom navigation function |
| `disableNavigationOnCompletion` | `boolean` | `undefined` | Disable navigation when flow is complete |
| `autoConfigureFromRoute` | `boolean` | `true` | Enable/disable automatic route-based configuration |

## Component Outputs

| Output | Type | Description |
|--------|------|-------------|
| `stepChanged` | `{ currentStep: ProcessedStep; stepIndex: number }` | Emitted when user navigates between steps |
| `navigationAttempted` | `{ targetStep: ProcessedStep; allowed: boolean }` | Emitted when user attempts navigation |

## Step Definition Interface

```typescript
interface StepDefinition {
  id: number;           // Unique step identifier
  route: string;        // Route segment to match
  title: string;        // Full step title
  shortTitle: string;   // Abbreviated title for compact display
  description: string;  // Step description for tooltips
  icon: string;         // PrimeIcons class name
}
```

## Styling

The component uses the established enterprise-grade color palette:
- Primary: `#6366f1`
- Success: `#10b981`
- Warning: `#f59e0b`
- Danger: `#ef4444`

All animations respect `prefers-reduced-motion` for accessibility.

## Examples

### In a Student Registration Component
```html
<!-- No configuration needed - automatically detects student registration flow -->
<app-dynamic-progress-steps></app-dynamic-progress-steps>

<div class="registration-content">
  <!-- Your registration form content -->
</div>
```

### In a Trial Request Component
```html
<!-- No configuration needed - automatically detects trial request flow -->
<app-dynamic-progress-steps></app-dynamic-progress-steps>

<div class="trial-request-content">
  <!-- Your trial request content -->
</div>
```

### With Custom Navigation
```html
<app-dynamic-progress-steps 
  [navigationCallback]="handleCustomNavigation">
</app-dynamic-progress-steps>
```

```typescript
handleCustomNavigation = (route: string) => {
  // Custom navigation logic
  console.log('Navigating to:', route);
  this.router.navigate([route]);
};
```

## Browser Support

- Modern browsers with ES2020+ support
- Responsive design works on mobile, tablet, and desktop
- Accessibility features support screen readers and keyboard navigation
