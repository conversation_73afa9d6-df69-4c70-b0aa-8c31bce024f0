import { CommonModule, Location } from '@angular/common';
import { Component, inject, signal, WritableSignal } from '@angular/core';
import { CheckboxModule } from 'primeng/checkbox';
import { DividerModule } from 'primeng/divider';
import { InputTextModule } from 'primeng/inputtext';
import { ButtonModule } from 'primeng/button';
import { RadioButtonModule } from 'primeng/radiobutton';
import { FormsModule } from '@angular/forms';
import { ICountry, IDialCodeDataDto } from 'SharedModules.Library';
import { DropdownModule } from 'primeng/dropdown';
import { RouterModule, Router, ActivatedRoute, NavigationEnd, ActivatedRouteSnapshot } from '@angular/router';
import { LoaderDirective } from '@platform.src/app/core/directives/loader.directive';
import { untilDestroyed } from 'SharedModules.Library';
import { AuthStateService } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { RegisterService } from '@platform.src/app/core/services/register.service';
import { UserService } from '@platform.src/app/core/services/user.service';
import { CardSplitLayoutComponent } from '@platform.src/app/shared/components/card-split-layout/card-split-layout.component';
import { filter } from 'rxjs';
import { ToastService } from 'SharedModules.Library';
import { HandleApiResponseService } from 'SharedModules.Library';
import { StepDefinitionConfig, STUDENT_REGISTRATION_CONFIG, STUDENT_REGISTRATION_STEPS, TRIAL_REQUEST_ADD_LANGUAGE_CONFIG, TRIAL_REQUEST_ADD_LANGUAGE_STEPS } from './student-registration-form/student-registration-step-definitions';
import { DynamicProgressStepsComponent, NavigationCallback, StepDefinition } from '@platform.app/shared/components/dynamic-progress-steps/dynamic-progress-steps.component';


@Component({
  selector: 'app-register-free-trial-steps',
  imports: [
    CommonModule,
    FormsModule,
    CheckboxModule,
    DividerModule,
    InputTextModule,
    ButtonModule,
    RadioButtonModule,
    DropdownModule,
    RouterModule,
    CardSplitLayoutComponent,
    LoaderDirective,
    DynamicProgressStepsComponent,
  ],
  templateUrl: './register-free-trial-steps.component.html',
  styleUrl: './register-free-trial-steps.component.scss'
})
export class RegisterFreeTrialStepsComponent {
  // Injected services
  router = inject(Router);
  apiService = inject(HandleApiResponseService);
  userService = inject(UserService);
  authService = inject(AuthStateService);
  registerService = inject(RegisterService);
  generalService = inject(GeneralService);
  toastService = inject(ToastService);
  location = inject(Location);

  // Signals and properties
  currentStep = signal(1);
  isAnotherFreeTrialPage = signal(false);
  studentsNum = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
  accountType: string = 'parent';
  selectedStudentsNum = 0;
  timezones: any[] = [];
  selectedTimezone!: any;
  countries = signal([] as ICountry[]);
  phoneCodes: WritableSignal<IDialCodeDataDto[]> = signal([]);

  selectedCountryCode = signal({} as IDialCodeDataDto);
  stepTitles: string[] = [
    "Create an Account",
    "Student's Information",
    "Student's Information",
    "Availability",
    "Student's More Details",
    "Student's Password",
    ""
  ];
  trialParagraphTexts = [{
    title: "Create a parent account",
  }, {
    title: "Register your students",
  }, {
    title: "Receive a free trial lesson",
  }];
  private untilDestroyed = untilDestroyed();


  // Progress steps configuration
  studentRegistrationSteps = [] as StepDefinition[];
  studentRegistrationConfig = {} as StepDefinitionConfig;

  constructor(private route: ActivatedRoute) {
  }

  get inEmptyPath(): boolean {
    const firstChild = this.route.snapshot.firstChild;
    return !firstChild || !firstChild.routeConfig || firstChild.routeConfig.path === '';
  }

  get totalStepsArray(): number[] {
    return Array(this.totalSteps).fill(0).map((x, i) => i + 1);
  }

  get totalSteps(): number {
    return this.selectedStudentsNum + 3;
  }

  ngOnInit(): void {
    this.logTimeZone();

    // Subscribe to route changes
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe(() => {
      // Call your method to find the page data on route change
      this.findFreeTrialPageData(this.route.snapshot);
    });

    this.processRouteData();
  }

  onStudentsNumChange(event: any) {
  }

  goToStep(step: number) {
    this.currentStep.set(step);
  }

  goToNextStep() {
    this.currentStep.set(this.currentStep() + 1);
  }

  goToPreviousStep() {
    if (this.currentStep() > 1) {
      this.currentStep.set(this.currentStep() - 1);
    }
  }

  submitParentFormButtonClicked(event: {}) {
    this.currentStep.set(this.currentStep() + 1);
  }

  // Navigation callback for progress steps
  handleStepNavigation: NavigationCallback = (route: string) => {
    this.registerService.navigateToNextStep(route);
  };

  private processRouteData() {
    this.findFreeTrialPageData(this.route.snapshot);
  }

  private findFreeTrialPageData(routeSnapshot: ActivatedRouteSnapshot) {
    console.log('findFreeTrialPageData', routeSnapshot);
    // If there are children, traverse them first
    if (routeSnapshot.children) {
      for (const child of routeSnapshot.children) {
        // Recursively search the children
        this.findFreeTrialPageData(child);

        // After checking children, check if the data was set
        if (this.isAnotherFreeTrialPage() !== undefined) {
          return; // Exit if we've already found the data
        }
      }
    }

    // Check if the current route has the data you're interested in
    if (routeSnapshot.data && routeSnapshot.data['isAnotherFreeTrialPage'] !== undefined) {
      this.isAnotherFreeTrialPage.set(routeSnapshot.data['isAnotherFreeTrialPage']);
    }

    if (routeSnapshot.data && routeSnapshot.data['steps'] !== undefined) {
      this.studentRegistrationSteps = routeSnapshot.data['steps'];
    } else {
      this.studentRegistrationSteps = [] as StepDefinition[];
    }

    if (routeSnapshot.data && routeSnapshot.data['stepsConfig'] !== undefined) {
      this.studentRegistrationConfig = routeSnapshot.data['stepsConfig'];
    } else {
      this.studentRegistrationConfig = {} as StepDefinitionConfig;
    }
  }

  goBackRoute() {
    const prevRoute = this.registerService.getTitleAndPrevRoute(this.router.url).prevRoute;

    if (prevRoute === 'back') {
      this.location.back();
    } else {
      this.router.navigate([this.registerService.getTitleAndPrevRoute(this.router.url).prevRoute]);
    }

  }

  private logTimeZone() {
    const timeZone = new Intl.DateTimeFormat().resolvedOptions().timeZone;
  }
}
