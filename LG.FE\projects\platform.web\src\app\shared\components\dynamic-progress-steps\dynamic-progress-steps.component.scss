@use "mixins";

:host {
  display: block;
  width: 100%;
}

.progress-steps-container {
  // Modern enterprise color palette
  --primary-color: #6366f1;
  --primary-light: #818cf8;
  --primary-dark: #4f46e5;
  --success-color: #10b981;
  --success-light: #34d399;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --surface-50: #f8fafc;
  --surface-100: #f1f5f9;
  --surface-200: #e2e8f0;
  --surface-300: #cbd5e1;
  --surface-400: #94a3b8;
  --surface-500: #64748b;
  --text-400: #9ca3af;
  --text-500: #64748b;
  --text-600: #475569;
  --text-700: #334155;
  --text-800: #1e293b;
  --text-900: #0f172a;

  // Ultra-compact spacing for modern progress bar
  --step-size: 1.25rem;
  --step-size-mobile: 1rem;
  --connector-height: 2px;
  --spacing-xs: 0.125rem;
  --spacing-sm: 0.25rem;
  --spacing-md: 0.375rem;
  --spacing-lg: 0.5rem;
}

// Modern Progress Bar with Integrated Steps
.progress-bar-container {
  position: relative;
  margin: var(--spacing-md) 0;

  .progress-bar-track {
    position: relative;
    background: var(--surface-200);
    height: var(--connector-height);
    border-radius: 1px;
    overflow: hidden;
  }

  .progress-bar-fill {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
    height: 100%;
    border-radius: 1px;
    transition: width 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.3) 50%, transparent 100%);
      animation: shimmer 2.5s infinite;
    }
  }
}

// Modern Step Navigation - Integrated with Progress Bar
.steps-navigation {
  position: relative;

  .step-item {
    position: relative;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 2;

    &.cursor-pointer:hover {
      .step-indicator {
        transform: scale(1.05);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      }

      .step-title {
        color: var(--text-900);
      }
    }
  }

  .step-indicator {
    width: var(--step-size);
    height: var(--step-size);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.625rem;
    font-weight: 600;
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    border: 2px solid var(--surface-300);
    background: white;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    position: relative;

    &.step-active {
      background: var(--primary-color);
      border-color: var(--primary-color);
      color: white;
      box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);

      &::after {
        content: '';
        position: absolute;
        inset: -3px;
        border-radius: 50%;
        background: var(--primary-color);
        opacity: 0.2;
        animation: pulse-ring 2s infinite;
      }
    }

    &.step-completed {
      background: var(--success-color);
      border-color: var(--success-color);
      color: white;
      box-shadow: 0 1px 4px rgba(16, 185, 129, 0.2);

      &:hover {
        background: var(--success-color);
        transform: scale(1.05);
      }
    }

    &.step-accessible {
      border-color: var(--surface-400);
      color: var(--text-500);

      &:hover {
        border-color: var(--primary-color);
        color: var(--primary-color);
      }
    }

    &.step-disabled {
      border-color: var(--surface-200);
      color: var(--text-400);
      opacity: 0.6;
    }
  }

  .step-title {
    font-size: 0.6875rem;
    font-weight: 500;
    color: var(--text-600);
    transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);
    margin-top: var(--spacing-sm);
    text-align: center;
    line-height: 1.2;
  }

  // Step connector line (hidden as we use the progress bar)
  .step-connector {
    display: none;
  }
}

// Mobile Progress Dots - Ultra Compact
.step-dot {
  width: 4px;
  height: 4px;
  border-radius: 50%;
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

  &.bg-primary {
    background: var(--primary-color);
    box-shadow: 0 0 0 2px rgba(99, 102, 241, 0.2);
  }

  &.bg-green-500 {
    background: var(--success-color);
    box-shadow: 0 0 0 1px rgba(16, 185, 129, 0.2);
  }

  &.bg-surface-300 {
    background: var(--surface-300);
  }
}

// Compact Current Step Summary
.current-step-summary {
  background: rgba(99, 102, 241, 0.03);
  border-left: 2px solid var(--primary-color);
  border-radius: 4px;
  padding: var(--spacing-md);
  margin-top: var(--spacing-lg);
  transition: all 0.15s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    background: rgba(99, 102, 241, 0.05);
  }

  .flex {
    gap: var(--spacing-sm);
  }

  i {
    font-size: 0.75rem;
    color: var(--primary-color);
  }

  .font-semibold {
    font-size: 0.75rem;
    font-weight: 600;
    color: var(--primary-color);
    margin: 0;
  }

  .text-xs {
    font-size: 0.6875rem;
    color: var(--text-600);
    margin: 0;
    line-height: 1.3;
  }
}

// Modern Animations
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-ring {
  0% {
    transform: scale(1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1.4);
    opacity: 0;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.2;
    transform: scale(1);
  }
  50% {
    opacity: 0.3;
    transform: scale(1.02);
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

// Responsive Design - Ultra Compact
@include mobile-only {
  .progress-steps-container {
    --step-size: var(--step-size-mobile);

    .progress-header {
      margin-bottom: var(--spacing-sm);

      .flex {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
      }

      h3 {
        font-size: 0.875rem;
        margin: 0;
      }

      .progress-info {
        font-size: 0.6875rem;
      }
    }

    .progress-bar-container {
      margin: var(--spacing-sm) 0;
    }

    .current-step-summary {
      padding: var(--spacing-sm);
      margin-top: var(--spacing-sm);

      .flex {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--spacing-xs);
      }
    }

    .steps-navigation {
      .step-title {
        font-size: 0.625rem;
      }
    }
  }
}

@include tablet-only {
  .progress-steps-container {
    .step-indicator {
      width: 1.125rem;
      height: 1.125rem;
      font-size: 0.5625rem;
    }

    .step-title {
      font-size: 0.625rem;
    }

    .progress-header h3 {
      font-size: 0.9375rem;
    }
  }
}

// Accessibility Enhancements
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// Focus States for Accessibility
.step-item:focus-visible,
.step-indicator:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .progress-bar-fill {
    background: var(--primary-color) !important;
  }
  
  .step-indicator {
    border-width: 3px !important;
  }
}

// Print Styles
@media print {
  .progress-steps-container {
    .progress-bar-container,
    .current-step-summary {
      display: none !important;
    }
    
    .steps-navigation {
      .step-item {
        break-inside: avoid;
      }
    }
  }
}
