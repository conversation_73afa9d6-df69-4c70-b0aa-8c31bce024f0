@use "mixins";

:host {
  display: block;
  width: 100%;
}

.progress-steps-container {
  // Refined color palette for subtle design
  --primary-color: #6366f1;
  --primary-light: #a5b4fc;
  --primary-subtle: #e0e7ff;
  --success-color: #10b981;
  --success-light: #6ee7b7;
  --success-subtle: #d1fae5;
  --surface-0: #ffffff;
  --surface-50: #f8fafc;
  --surface-100: #f1f5f9;
  --surface-200: #e2e8f0;
  --surface-300: #cbd5e1;
  --surface-400: #94a3b8;
  --surface-500: #64748b;

  // Refined spacing system
  --step-size-desktop: 2rem;
  --step-size-tablet: 1.75rem;
  --step-size-mobile: 1.5rem;
  --progress-height: 4px;
  --border-radius: 6px;
  --border-radius-full: 50%;

  // Spacing scale
  --space-1: 0.25rem;   // 4px
  --space-2: 0.5rem;    // 8px
  --space-3: 0.75rem;   // 12px
  --space-4: 1rem;      // 16px
  --space-5: 1.25rem;   // 20px
  --space-6: 1.5rem;    // 24px
}

// Progress Header - Clear and Intuitive
.progress-header {
  margin-bottom: var(--space-4);

  .header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: var(--space-3);
  }

  .flow-title {
    font-size: 1rem;
    font-weight: 600;
    // color: var(--gray-900);
    margin: 0;
    letter-spacing: -0.025em;
  }

  .step-counter {
    display: flex;
    align-items: center;
    gap: var(--space-2);
    font-size: 0.875rem;
    color: var(--gray-600);
    font-weight: 500;

    .current-step {
      color: var(--primary-color);
      font-weight: 600;
    }

    .separator {
      color: var(--gray-400);
    }
  }
}

  .progress-subtitle {
    font-size: 1rem;
    color: var(--primary-600);
    font-weight: 600;
  }

// Subtle Progress Track
.progress-track-container {
  position: relative;
  margin: var(--space-4) 0 var(--space-5) 0;

  .progress-track {
    position: relative;
    background: var(--surface-200);
    height: var(--progress-height);
    border-radius: var(--border-radius);
    overflow: hidden;
  }

  .progress-fill {
    background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 100%);
    height: 100%;
    border-radius: var(--border-radius);
    transition: width 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.4) 50%, transparent 100%);
      animation: shimmer 3s infinite;
    }
  }

  .progress-percentage {
    position: absolute;
    top: 100%;
    right: 0;
    margin-top: var(--space-1);
    font-size: 0.75rem;
    color: var(--gray-500);
    font-weight: 500;
  }
}

// Intuitive Step Navigation
.steps-navigation {
  position: relative;

  .steps-container {
    display: flex;
    align-items: flex-start;
    justify-content: space-between;
    position: relative;
    z-index: 2;
  }

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    flex: 1;
    position: relative;
    transition: all 0.2s ease-out;
    cursor: pointer;

    &:hover:not(.step-disabled) {
      .step-indicator {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }

      .step-label {
        color: var(--gray-900);
      }
    }

    &.step-disabled {
      cursor: not-allowed;
      // opacity: 0.6;
    }
  }

  .step-indicator {
    width: var(--step-size-desktop);
    height: var(--step-size-desktop);
    border-radius: var(--border-radius-full);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    font-weight: 600;
    transition: all 0.2s ease-out;
    border: 2px solid var(--surface-300);
    background: var(--surface-0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.06);
    position: relative;
    margin-bottom: var(--space-2);

    &.step-active {
      background: var(--primary-color);
      border-color: var(--primary-color);
      color: var(--surface-0);
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.25);

      &::before {
        content: '';
        position: absolute;
        inset: -4px;
        border-radius: var(--border-radius-full);
        background: var(--primary-subtle);
        z-index: -1;
        animation: pulse-subtle 2s infinite;
      }
    }

    &.step-completed {
      background: var(--success-color);
      border-color: var(--success-color);
      color: var(--surface-0);
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);

      &:hover {
        background: var(--success-color);
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
      }
    }

    &.step-accessible {
      border-color: var(--surface-400);
      color: var(--gray-500);
      background: var(--surface-50);

      &:hover {
        border-color: var(--primary-light);
        background: var(--primary-subtle);
        color: var(--primary-color);
      }
    }

    &.step-disabled {
      border-color: var(--surface-200);
      color: var(--gray-300);
      background: var(--surface-50);
    }
  }

  .step-label {
    font-size: 0.8125rem;
    font-weight: 500;
    color: var(--gray-600);
    transition: all 0.2s ease-out;
    line-height: 1.3;
    max-width: 80px;

    &.step-active {
      color: var(--primary-color);
      font-weight: 600;
    }

    &.step-completed {
      color: var(--success-color);
      font-weight: 600;
    }
  }

  // Connection line between steps
  .step-connector {
    position: absolute;
    top: calc(var(--step-size-desktop) / 2);
    left: calc(var(--step-size-desktop) / 2);
    right: calc(var(--step-size-desktop) / 2);
    height: 2px;
    background: var(--surface-200);
    z-index: 1;

    &.connector-completed {
      background: linear-gradient(90deg, var(--success-color) 0%, var(--primary-color) 100%);
    }
  }
}

// Mobile Step Navigation
.mobile-steps {
  .current-step-card {
    background: var(--surface-50);
    border: 1px solid var(--surface-200);
    border-radius: var(--border-radius);
    padding: var(--space-3);
    margin-bottom: var(--space-3);

    .step-info {
      display: flex;
      align-items: center;
      gap: var(--space-3);
    }

    .mobile-step-indicator {
      width: var(--step-size-mobile);
      height: var(--step-size-mobile);
      border-radius: var(--border-radius-full);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 0.75rem;
      font-weight: 600;
      flex-shrink: 0;

      &.step-active {
        background: var(--primary-color);
        color: var(--surface-0);
      }

      &.step-completed {
        background: var(--success-color);
        color: var(--surface-0);
      }
    }

    .step-details {
      flex: 1;

      .step-title {
        font-size: 0.875rem;
        font-weight: 600;
        color: var(--gray-900);
        margin: 0 0 var(--space-1) 0;
      }

      .step-description {
        font-size: 0.8125rem;
        color: var(--gray-600);
        margin: 0;
        line-height: 1.4;
      }
    }

    .step-counter {
      font-size: 0.75rem;
      color: var(--gray-500);
      font-weight: 500;
    }
  }

  .progress-dots {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: var(--space-2);

    .progress-dot {
      width: 6px;
      height: 6px;
      border-radius: var(--border-radius-full);
      background: var(--surface-300);
      transition: all 0.2s ease-out;

      &.dot-active {
        background: var(--primary-color);
        transform: scale(1.2);
      }

      &.dot-completed {
        background: var(--success-color);
      }
    }
  }
}

// Current Step Summary
.current-step-summary {
  background: var(--primary-subtle);
  border: 1px solid var(--primary-light);
  border-radius: var(--border-radius);
  padding: var(--space-3);
  margin-top: var(--space-4);

  .summary-content {
    display: flex;
    align-items: center;
    gap: var(--space-3);
  }

  .summary-icon {
    width: 2rem;
    height: 2rem;
    border-radius: var(--border-radius-full);
    background: var(--primary-color);
    color: var(--surface-0);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
  }

  .summary-text {
    flex: 1;

    .summary-title {
      font-size: 0.875rem;
      font-weight: 600;
      color: var(--primary-color);
      margin: 0 0 var(--space-1) 0;
    }

    .summary-description {
      font-size: 0.8125rem;
      color: var(--gray-700);
      margin: 0;
      line-height: 1.4;
    }
  }
}

// Refined Animations
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse-subtle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 0.6;
    transform: scale(1.05);
  }
}

@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(8px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fade-in 0.3s ease-out;
}

// Responsive Design using proper mixins
@include mixins.breakpoint(mobile) {
  .progress-steps-container {
    .progress-header {
      margin-bottom: var(--space-3);

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: var(--space-2);
      }

      .flow-title {
        font-size: 0.9375rem;
      }

      .step-counter {
        font-size: 0.8125rem;
      }
    }

    .progress-track-container {
      margin: var(--space-3) 0;
    }

    .steps-navigation {
      .step-indicator {
        width: var(--step-size-mobile);
        height: var(--step-size-mobile);
        font-size: 0.75rem;
        margin-bottom: var(--space-1);
      }

      .step-label {
        font-size: 0.75rem;
        max-width: 60px;
      }

      .step-connector {
        top: calc(var(--step-size-mobile) / 2);
        left: calc(var(--step-size-mobile) / 2);
        right: calc(var(--step-size-mobile) / 2);
      }
    }

    .current-step-summary {
      padding: var(--space-2);
      margin-top: var(--space-3);

      .summary-content {
        gap: var(--space-2);
      }

      .summary-icon {
        width: 1.5rem;
        height: 1.5rem;
        font-size: 0.75rem;
      }

      .summary-title {
        font-size: 0.8125rem;
      }

      .summary-description {
        font-size: 0.75rem;
      }
    }
  }
}

@include mixins.breakpoint(tablet) {
  .progress-steps-container {
    .steps-navigation {
      .step-indicator {
        width: var(--step-size-tablet);
        height: var(--step-size-tablet);
        font-size: 0.8125rem;
      }

      .step-label {
        font-size: 0.75rem;
        max-width: 70px;
      }

      .step-connector {
        top: calc(var(--step-size-tablet) / 2);
        left: calc(var(--step-size-tablet) / 2);
        right: calc(var(--step-size-tablet) / 2);
      }
    }
  }
}

@include mixins.breakpoint(desktop) {
  .progress-steps-container {
    .steps-navigation {
      .step-connector {
        top: calc(var(--step-size-desktop) / 2);
        left: calc(var(--step-size-desktop) / 2);
        right: calc(var(--step-size-desktop) / 2);
      }
    }
  }
}

// Accessibility & Polish
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// Enhanced Focus States
.step-item {
  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 3px;
    border-radius: var(--border-radius);
  }
}

.step-indicator {
  &:focus-visible {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
  }
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .progress-fill {
    background: var(--primary-color) !important;
  }

  .step-indicator {
    border-width: 3px !important;

    &.step-active,
    &.step-completed {
      border-width: 4px !important;
    }
  }

  .step-label {
    font-weight: 600 !important;
  }
}

// Dark Mode Support (if needed)
@media (prefers-color-scheme: dark) {
  .progress-steps-container {
    --surface-0: #1e293b;
    --surface-50: #334155;
    --surface-100: #475569;
    --surface-200: #64748b;
    --gray-900: #f8fafc;
    --gray-700: #e2e8f0;
    --gray-600: #cbd5e1;
    --gray-500: #94a3b8;
  }
}

// Print Styles
@media print {
  .progress-steps-container {
    .progress-track-container,
    .current-step-summary,
    .mobile-steps {
      display: none !important;
    }

    .steps-navigation {
      .step-item {
        break-inside: avoid;
        page-break-inside: avoid;
      }
    }
  }
}
