@use "mixins";

:host {
  display: block;
  width: 100%;
}

.progress-steps-container {
  // Enterprise-grade color palette
  --primary-color: #6366f1;
  --success-color: #10b981;
  --warning-color: #f59e0b;
  --danger-color: #ef4444;
  --surface-50: #f8fafc;
  --surface-100: #f1f5f9;
  --surface-200: #e2e8f0;
  --surface-300: #cbd5e1;
  --text-600: #475569;
  --text-700: #334155;
  --text-900: #0f172a;
}

// Progress Bar Styling
.progress-bar-container {
  .progress-bar-track {
    position: relative;
    background: var(--surface-200);
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.05);
  }

  .progress-bar-fill {
    background: linear-gradient(90deg, var(--primary-color) 0%, #8b5cf6 100%);
    box-shadow: 0 1px 3px rgba(99, 102, 241, 0.3);
    position: relative;
    
    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
      animation: shimmer 2s infinite;
    }
  }
}

// Step Navigation Styling
.steps-navigation {
  .step-item {
    position: relative;
    transition: all 0.2s ease-in-out;

    &.cursor-pointer:hover {
      transform: translateY(-2px);
      
      .step-indicator {
        transform: scale(1.02);
      }
    }
  }

  .step-indicator {
    position: relative;
    transition: all 0.2s ease-in-out;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    
    &.step-active {
      background: var(--primary-color) !important;
      border-color: var(--primary-color) !important;
      box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
    }

    &.step-completed {
      background: var(--success-color) !important;
      border-color: var(--success-color) !important;
      box-shadow: 0 2px 8px rgba(16, 185, 129, 0.2);

      &:hover {
        background: #059669 !important;
        transform: scale(1.02);
      }
    }

    &.step-accessible {
      &:hover {
        background: var(--surface-200) !important;
        border-color: var(--surface-300) !important;
        transform: scale(1.02);
      }
    }

    &.step-disabled {
      opacity: 0.6;
    }
  }

  .step-title {
    transition: all 0.2s ease-in-out;
    
    &:hover {
      transform: translateY(-1px);
    }
  }

  .step-connector {
    transition: all 0.3s ease-in-out;
  }
}

// Mobile Specific Styling
.step-dot {
  transition: all 0.2s ease-in-out;
  
  &.bg-primary {
    box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.2);
  }
  
  &.bg-green-500 {
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.2);
  }
}

// Current Step Summary
.current-step-summary {
  background: rgba(99, 102, 241, 0.05) !important;
  border-left-color: var(--primary-color) !important;
  transition: all 0.2s ease-in-out;
  
  &:hover {
    background: rgba(99, 102, 241, 0.08) !important;
  }
}

// Animations
@keyframes shimmer {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.25;
    transform: scale(1);
  }
  50% {
    opacity: 0.4;
    transform: scale(1.05);
  }
}

.animate-pulse {
  animation: pulse 2s infinite;
}

// Responsive Design using mixins
@include mobile-only {
  .progress-steps-container {
    .progress-header {
      .flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
      }
    }
    
    .current-step-summary {
      padding: 0.75rem !important;
      
      .flex {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 0.5rem;
      }
    }
  }
}

@include tablet-only {
  .step-indicator {
    width: 2.5rem !important;
    height: 2.5rem !important;
    font-size: 0.875rem !important;
  }
  
  .step-title {
    font-size: 0.75rem !important;
  }
}

// Accessibility Enhancements
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// Focus States for Accessibility
.step-item:focus-visible,
.step-indicator:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// High Contrast Mode Support
@media (prefers-contrast: high) {
  .progress-bar-fill {
    background: var(--primary-color) !important;
  }
  
  .step-indicator {
    border-width: 3px !important;
  }
}

// Print Styles
@media print {
  .progress-steps-container {
    .progress-bar-container,
    .current-step-summary {
      display: none !important;
    }
    
    .steps-navigation {
      .step-item {
        break-inside: avoid;
      }
    }
  }
}
