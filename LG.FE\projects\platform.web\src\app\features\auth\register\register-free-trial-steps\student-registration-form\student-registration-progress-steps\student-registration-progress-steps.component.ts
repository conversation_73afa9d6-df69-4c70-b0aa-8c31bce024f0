import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal, computed } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter } from 'rxjs/operators';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';
import { RegisterService } from '@platform.app/core/services/register.service';

export interface RegistrationStep {
  id: number;
  route: string;
  title: string;
  shortTitle: string;
  description: string;
  icon: string;
  isCompleted: boolean;
  isActive: boolean;
  isAccessible: boolean;
}

@Component({
  selector: 'app-student-registration-progress-steps',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TooltipModule
  ],
  templateUrl: './student-registration-progress-steps.component.html',
  styleUrl: './student-registration-progress-steps.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class StudentRegistrationProgressStepsComponent implements OnInit {
  // Injected services
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly registerService = inject(RegisterService);
  private readonly destroyRef = inject(DestroyRef);

  // Component state
  currentRoute = signal<string>('');
  currentStepIndex = signal<number>(0);

  // Step definitions
  private readonly stepDefinitions: Omit<RegistrationStep, 'isCompleted' | 'isActive' | 'isAccessible'>[] = [
    {
      id: 1,
      route: 'student-name',
      title: 'Student Name',
      shortTitle: 'Name',
      description: 'Enter student\'s full name',
      icon: 'pi pi-user'
    },
    {
      id: 2,
      route: 'student-info',
      title: 'Student Information',
      shortTitle: 'Info',
      description: 'Basic student details and preferences',
      icon: 'pi pi-info-circle'
    },
    {
      id: 3,
      route: 'student-availability',
      title: 'Availability',
      shortTitle: 'Schedule',
      description: 'Set learning schedule preferences',
      icon: 'pi pi-calendar'
    },
    {
      id: 4,
      route: 'student-more-details',
      title: 'Additional Details',
      shortTitle: 'Details',
      description: 'Complete student profile information',
      icon: 'pi pi-file-edit'
    },
    {
      id: 5,
      route: 'register-success',
      title: 'Registration Complete',
      shortTitle: 'Complete',
      description: 'Registration successfully completed',
      icon: 'pi pi-check-circle'
    }
  ];

  // Computed properties
  steps = computed<RegistrationStep[]>(() => {
    const currentRoute = this.currentRoute();
    const currentIndex = this.currentStepIndex();

    return this.stepDefinitions.map((step, index) => ({
      ...step,
      isActive: step.route === currentRoute,
      isCompleted: index < currentIndex,
      isAccessible: index <= currentIndex
    }));
  });

  progressPercentage = computed<number>(() => {
    const currentIndex = this.currentStepIndex();
    const totalSteps = this.stepDefinitions.length;
    return Math.round((currentIndex / (totalSteps - 1)) * 100);
  });

  activeStep = computed<RegistrationStep | undefined>(() => {
    return this.steps().find(step => step.isActive);
  });

  ngOnInit(): void {
    // Initialize current route
    this.updateCurrentRoute();

    // Listen to route changes
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        this.updateCurrentRoute();
      });
  }

  /**
   * Updates the current route and step index based on the current URL
   */
  private updateCurrentRoute(): void {
    const url = this.router.url;
    const matchedStep = this.stepDefinitions.find(step => url.includes(step.route));
    
    if (matchedStep) {
      this.currentRoute.set(matchedStep.route);
      this.currentStepIndex.set(matchedStep.id - 1);
    }
  }

  /**
   * Navigates to a specific step if it's accessible
   */
  navigateToStep(step: RegistrationStep): void {
    if (!step.isAccessible || step.isActive) {
      return;
    }

    // Use RegisterService navigation method to maintain consistency
    this.registerService.navigateToNextStep(step.route);
  }

  /**
   * Gets the CSS classes for a step indicator
   */
  getStepIndicatorClasses(step: RegistrationStep): string {
    const baseClasses = 'step-indicator flex align-items-center justify-content-center border-circle transition-all duration-200';
    
    if (step.isActive) {
      return `${baseClasses} step-active bg-primary text-white border-2 border-primary`;
    } else if (step.isCompleted) {
      return `${baseClasses} step-completed bg-green-500 text-white border-2 border-green-500 cursor-pointer hover:bg-green-600`;
    } else if (step.isAccessible) {
      return `${baseClasses} step-accessible bg-surface-100 text-600 border-2 border-300 cursor-pointer hover:bg-surface-200`;
    } else {
      return `${baseClasses} step-disabled bg-surface-50 text-400 border-2 border-200`;
    }
  }

  /**
   * Gets the CSS classes for a step title
   */
  getStepTitleClasses(step: RegistrationStep): string {
    const baseClasses = 'step-title text-sm font-medium transition-colors duration-200';
    
    if (step.isActive) {
      return `${baseClasses} text-primary`;
    } else if (step.isCompleted) {
      return `${baseClasses} text-green-600 cursor-pointer hover:text-green-700`;
    } else if (step.isAccessible) {
      return `${baseClasses} text-600 cursor-pointer hover:text-700`;
    } else {
      return `${baseClasses} text-400`;
    }
  }

  /**
   * Gets the tooltip text for a step
   */
  getStepTooltip(step: RegistrationStep): string {
    if (step.isActive) {
      return `Current step: ${step.description}`;
    } else if (step.isCompleted) {
      return `Completed: ${step.description} (Click to revisit)`;
    } else if (step.isAccessible) {
      return `Next: ${step.description}`;
    } else {
      return `Upcoming: ${step.description}`;
    }
  }
}
