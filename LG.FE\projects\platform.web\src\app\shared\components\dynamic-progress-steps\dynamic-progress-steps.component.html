<!-- Modern Progress Steps Container -->
<div class="progress-steps-container w-full mb-2">
  <!-- Compact Progress Header -->
  <div class="progress-header">
    <div class="flex align-items-center justify-content-between">
      <h3 class="text-sm font-semibold text-900 m-0">{{ effectiveConfig().flowTitle }}</h3>
      <div class="progress-info text-xs text-500 font-medium">
        {{ currentStepIndex() + 1 }}/{{ effectiveConfig().steps.length }}
      </div>
    </div>
  </div>

  <!-- Integrated Progress Bar with Steps -->
  <div class="steps-navigation relative">
    <!-- Progress Bar Background -->
    <div class="progress-bar-container absolute w-full" style="top: 50%; transform: translateY(-50%); z-index: 1;">
      <div class="progress-bar-track w-full">
        <div
          class="progress-bar-fill"
          [style.width.%]="progressPercentage()">
        </div>
      </div>
    </div>

  <!-- Steps Navigation -->
  <div class="steps-navigation">
    <!-- Desktop View - Steps over Progress Bar -->
    <div class="hidden md:block relative z-10">
      <div class="flex align-items-center justify-content-between">
        <div
          *ngFor="let step of steps(); let i = index"
          class="step-item flex flex-column align-items-center"
          [class.cursor-pointer]="step.isAccessible && !step.isActive"
          (click)="navigateToStep(step)"
          [pTooltip]="getStepTooltip(step)"
          tooltipPosition="top">

          <!-- Step Indicator -->
          <div
            [class]="getStepIndicatorClasses(step)"
            class="step-indicator">
            <i [class]="step.icon" *ngIf="!step.isCompleted"></i>
            <i class="pi pi-check" *ngIf="step.isCompleted"></i>
          </div>

          <!-- Step Title -->
          <div [class]="getStepTitleClasses(step)" class="step-title">
            {{ step.shortTitle }}
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile View - Compact -->
    <div class="block md:hidden">
      <!-- Mobile Progress Bar -->
      <div class="progress-bar-container mb-2">
        <div class="progress-bar-track w-full">
          <div
            class="progress-bar-fill"
            [style.width.%]="progressPercentage()">
          </div>
        </div>
      </div>

      <!-- Current Step Info -->
      <div class="flex align-items-center gap-2 p-2 bg-surface-50 border-round">
        <div [class]="getStepIndicatorClasses(activeStep()!)" class="step-indicator">
          <i [class]="activeStep()?.icon" *ngIf="!activeStep()?.isCompleted"></i>
          <i class="pi pi-check" *ngIf="activeStep()?.isCompleted"></i>
        </div>

        <div class="flex-1 text-left">
          <div class="font-semibold text-900 text-xs">
            {{ activeStep()?.title }}
          </div>
          <div class="text-xs text-500">
            {{ activeStep()?.description }}
          </div>
        </div>

        <div class="text-xs font-semibold text-primary">
          {{ currentStepIndex() + 1 }}/{{ steps().length }}
        </div>
      </div>

      <!-- Mobile Steps Dots -->
      <div class="flex align-items-center justify-content-center gap-1 mt-2">
        <div
          *ngFor="let step of steps()"
          class="step-dot"
          [class.bg-primary]="step.isActive"
          [class.bg-green-500]="step.isCompleted"
          [class.bg-surface-300]="!step.isActive && !step.isCompleted">
        </div>
      </div>
    </div>
  </div>

  <!-- Compact Current Step Summary -->
  <div class="current-step-summary" *ngIf="activeStep()">
    <div class="flex align-items-center">
      <i [class]="activeStep()?.icon"></i>
      <div>
        <div class="font-semibold">
          {{ activeStep()?.title }}
        </div>
        <div class="text-xs">
          {{ activeStep()?.description }}
        </div>
      </div>
    </div>
  </div>
</div>
