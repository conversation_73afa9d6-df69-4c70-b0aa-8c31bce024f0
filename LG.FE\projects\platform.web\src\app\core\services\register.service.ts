import { IRegisterStudentRequest, ITimezoneData, ITimeZoneIdData } from 'SharedModules.Library';
import { IRegisterParentRequest, IRegistrationTypeEnum } from 'SharedModules.Library';
import { Injectable, inject } from '@angular/core';
import { NavigationExtras, Router } from '@angular/router';

import { GeneralService } from 'SharedModules.Library';
import { AuthStateService } from 'SharedModules.Library';
import { FreeTrialReasonModeEnum } from 'SharedModules.Library';

@Injectable({
  providedIn: 'root'
})
export class RegisterService {
  private registerStudentRequestKey = 'registerStudentRequest';
  private registerParentRequestKey = 'registerParentRequest';
  private registerTrialRequestKey = 'registerTrialRequest';
  private isRequestingTrialKey = 'isRequestingTrial';

  router = inject(Router);
  authService = inject(AuthStateService);
  generalService = inject(GeneralService);
  private _registerStudentRequest: Partial<IRegisterStudentRequest | unknown> = {
    addressDto: {
      city: '',
      postCode: '',
      state: '',
      addressLine1: '',
      addressLine2: ''
    },
    firstName: '',
    lastName: '',
    dateOfBirth: undefined,
    gender: 1,
    languageName: '',
    languageOfInterestId: '',
    timeZoneIana: {} as ITimezoneData,
    speakingLanguageDtos: [],
    parentId: this.authService.getUserClaims()?.id,
    countryOfResidence: '',
    weekDayTimeSlots: []
  };

  private _registerTrialRequest: Partial<IRegisterStudentRequest | unknown> = {
    firstName: '',
    lastName: '',
    dateOfBirth: undefined,
    gender: 1,
    languageOfInterest: {
      languageOfInterestId: undefined,
      languageOfInterest: '',
      availability: undefined,
      moreDetails: '',
      availabilitySupportedText: '',
      level: undefined
    },
    timeZoneIana: '',
    nativeLanguage: '',
    parentId: this.authService.getUserClaims()?.id,
    countryOfResidence: '',
  };
  trialRequestReason: FreeTrialReasonModeEnum | undefined = undefined;

  constructor() { }

  private _registerParentRequest: IRegisterParentRequest = {
    firstName: '',
    lastName: '',
    emailAddress: '',
    googleIdToken: '',
    registrationType: IRegistrationTypeEnum.Otp
  };

  get registerStudentRequest(): Partial<IRegisterStudentRequest | unknown> {
    const storedData = this.retrieve(this.registerStudentRequestKey);
    return storedData ? storedData : this._registerStudentRequest;
  }

  set registerStudentRequest(data: IRegisterStudentRequest) {
    this._registerStudentRequest = data;
    this.save(this.registerStudentRequestKey, data);
  }


  get registerParentRequest(): IRegisterParentRequest {
    const storedData = this.retrieve(this.registerParentRequestKey);
    return storedData ? storedData : this._registerParentRequest;
  }

  set registerParentRequest(data: IRegisterParentRequest) {
    this._registerParentRequest = data;
    this.save(this.registerParentRequestKey, data);
  }

  // Getter and setter for registerTrialRequest

  // Getter and setter for isRequestingTrial
  get isRequestingTrial(): boolean {
    return sessionStorage.getItem(this.isRequestingTrialKey) === 'true';
  }

  set isRequestingTrial(value: boolean) {
    sessionStorage.setItem(this.isRequestingTrialKey, value.toString());
  }

  // Method to start trial request
  setTrialRequestReason(reason: FreeTrialReasonModeEnum | undefined) {
    this.trialRequestReason = reason;
  }

  getTrialRequestReason(): FreeTrialReasonModeEnum | undefined {
    return this.trialRequestReason;
  }

  // Method to start trial request
  startTrialRequest() {
    this.isRequestingTrial = true;
  }

  // Method to end trial request
  endTrialRequest() {
    this.isRequestingTrial = false;
  }

  // Method to check if a trial request is currently in progress
  isCurrentlyRequestingTrial(): boolean {
    return this.isRequestingTrial;
  }

  updateRegisterParentRequest(newData: Partial<IRegisterParentRequest>) {
    const updatedData = { ...this.registerParentRequest, ...newData };
    this.registerParentRequest = updatedData;
  }

  clearRegisterParentRequest() {
    this.registerParentRequest = {
      firstName: '',
      lastName: '',
      emailAddress: '',
      googleIdToken: '',
      registrationType: IRegistrationTypeEnum.Otp
    };
    this.clear(this.registerParentRequestKey);
  }

  updateRegisterStudentRequest(newData: Partial<IRegisterStudentRequest>) {
    const updatedData = { ...this.registerStudentRequest, ...newData };
    this.registerStudentRequest = updatedData as IRegisterStudentRequest;
  }

  getRegisterStudentRequest(): any {
    return this.registerStudentRequest;
  }

  getRegisterParentRequest(): IRegisterParentRequest {
    return this.registerParentRequest;
  }

  clearRegisterStudentRequest() {
    this.registerStudentRequest = {
      firstName: '',
      lastName: '',
      dateOfBirth: undefined,
      gender: 1,
      languageOfInterestId: '',
      timeZoneIana: '',
      speakingLanguageDtos: [],
      parentId: this.authService.getUserClaims()?.id,
      weekDayTimeSlots: [],
      addressDto: {
        country: '',
        city: '',
        postCode: '',
        state: '',
        addressLine1: '',
        addressLine2: ''
      }
    };
  }

  goToNewLanguage(studentId?: string): void {
    this.navigateToNewTrialStep('trial-request-add-new-language', {
      queryParams: {
        studentId: studentId
      }
    });
  }

  navigateToNewTrialStep(nextStep: string, extras?: NavigationExtras, queryParams?: { [key: string]: any }) {
    console.log('navigateToNewTrialStep', nextStep, extras, queryParams);

    this.router.navigate(['/dashboard/parent/request-free-trial/reason', nextStep], {
      ...extras,
      queryParams: queryParams ? queryParams : undefined
    });
    // Navigate to the next step route with optional navigation extras and query parameters
  }

  navigateToNextStep(nextStep: string, extras?: NavigationExtras, queryParams?: { [key: string]: any }) {
    this.router.navigate(['/dashboard/parent/request-free-trial', nextStep], {
      ...extras,
      queryParams: queryParams ? queryParams : undefined
    });
    // Navigate to the next step route with optional navigation extras and query parameters
  }

  getTitleAndPrevRoute(route: string): { title: string, subtitle?: string, prevRoute: string | undefined } {
    let prevRoute = undefined;
    if (route.includes('register-otp')) {
      prevRoute = undefined;
      return { title: 'One Time Password', prevRoute };
    } else if (route.includes('student-name')) {
      prevRoute = 'back';
      return { title: 'Student’s Information', prevRoute };
    } else if (route.includes('student-info')) {
      prevRoute = 'back';
      return { title: 'Student Information', prevRoute };
    } else if (route.includes('student-availability')) {
      prevRoute = 'back';
      return { title: 'Student Availability', prevRoute };
    } else if (route.includes('student-more-details')) {
      prevRoute = 'back';
      return { title: 'Student More Details', prevRoute };
    } else if (route.includes('student-set-password')) {
      prevRoute = 'student-more-details';
      return { title: 'Set Password', prevRoute };
    } else if (route.includes('register-success')) {
      prevRoute = undefined;
      return { title: 'Registration Success', prevRoute };
    } else if (route.includes('social-auth')) {
      prevRoute = undefined;
      return { title: 'Complete Registration', prevRoute };
    } else if (route.includes('trial-request-choose-student')) {
      prevRoute = undefined;
      return { title: 'Choose a student', prevRoute };
    } else if (route.includes('trial-request-choose-language')) {
      prevRoute = 'trial-request-choose-student';
      return { title: 'Choose a language', prevRoute };
    } else if (route.includes('trial-request-add-new-language')) {
      prevRoute = 'back';
      return { title: 'Select new language for student', prevRoute };
    } else if (route.includes('free-trial-reason')) {
      prevRoute = undefined;
      return { title: 'What is your reason for requesting a free trial?', prevRoute };
    } else if (route.includes('free-trial-thank-you')) {
      prevRoute = undefined;
      return { title: '', prevRoute };
    } else {
      prevRoute = undefined;
      return { title: 'Create Parent Account', prevRoute };
    }
  }

  getRouteRegisterStepNumber(route: string): number {
    switch (true) {
      case route.includes('register-otp'):
        return 1;
      case route.includes('student-name'):
        return 2;
      case route.includes('student-info'):
        return 3;
      case route.includes('student-availability'):
        return 4;
      case route.includes('student-more-details'):
        return 5;
      case route.includes('register-success'):
        return 7;
      case route.includes('social-auth'):
        return 8;
      default:
        return 9;
    }
  }

  goToRegisterNewStudent(withNavigate: boolean = true, queryParams?: { [key: string]: any }): boolean | string {
    this.endTrialRequest();
    this.clearRegisterStudentRequest();

    if (withNavigate) {
      this.generalService.navigateTo('/dashboard/parent/request-free-trial/student-name', undefined, queryParams);
      return false;
    } else {
      return '/dashboard/parent/request-free-trial/student-name';
    }
  }


  goToNewLanguageForStudent(studentId?: string | number): void {
    const queryParams: { [key: string]: any } = {};

    // Only add studentId to queryParams if it’s provided and valid
    if (studentId !== undefined && studentId !== null) {
      queryParams['studentId'] = studentId;
    }

    this.navigateToNewTrialStep('trial-request-add-new-language', { queryParams });
  }

  save(key: string, data: any) {
    sessionStorage.setItem(key, JSON.stringify(data));
  }

  retrieve(key: string): any {
    const data = sessionStorage.getItem(key);
    return data ? JSON.parse(data) : null;
  }

  clear(key: string) {
    sessionStorage.removeItem(key);
  }

  clearAll() {
    sessionStorage.clear();
  }
}
