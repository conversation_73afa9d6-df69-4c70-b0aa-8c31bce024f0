@use "mixins";

// Modern color palette
$primary: #6366f1;
$primary-50: #eef2ff;
$primary-100: #e0e7ff;
$primary-600: #4f46e5;
$primary-700: #4338ca;

$success: #10b981;
$success-50: #ecfdf5;
$success-100: #d1fae5;

$orange: #f97316;
$orange-50: #fff7ed;
$orange-100: #ffedd5;

$purple: #8b5cf6;
$purple-50: #f3e8ff;
$purple-100: #e9d5ff;

$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// Professional shadows
$shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
$shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.1);
$shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -2px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -4px rgba(0, 0, 0, 0.1);

// Smooth transitions
$transition-fast: all 0.15s ease-out;
$transition-base: all 0.2s ease-out;
$transition-slow: all 0.3s ease-out;

:host {
  display: block;
}

// Galaxy Learning Journey Notification
.galaxy-journey-notification {
  margin: 0;
  display: flex;
  justify-content: center;

  @include mixins.breakpoint(mobile) {
    margin: 0.875rem 0;
  }

  .journey-card {
    background: linear-gradient(135deg, #1e1b4b 0%, #312e81 50%, #4c1d95 100%);
    border-radius: 12px;
    padding: 1rem;
    border: 1px solid rgba(139, 92, 246, 0.3);
    box-shadow: 0 4px 20px rgba(139, 92, 246, 0.2);
    width: 100%;
    position: relative;
    overflow: hidden;
    transition: $transition-base;

    @include mixins.breakpoint(mobile) {
      padding: 0.875rem;
      border-radius: 10px;
    }

    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 8px 25px rgba(139, 92, 246, 0.3);
      border-color: rgba(139, 92, 246, 0.5);
    }

    .cosmic-background {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      pointer-events: none;

      .star {
        position: absolute;
        width: 2px;
        height: 2px;
        background: rgba(255, 255, 255, 0.8);
        border-radius: 50%;
        animation: twinkle 3s ease-in-out infinite;

        &.star-1 {
          top: 20%;
          left: 15%;
          animation-delay: 0s;
        }

        &.star-2 {
          top: 60%;
          right: 20%;
          animation-delay: 1s;
        }

        &.star-3 {
          bottom: 30%;
          left: 70%;
          animation-delay: 2s;
        }
      }
    }

    .journey-content {
      position: relative;
      z-index: 2;
      display: flex;
      align-items: center;
      gap: 0.875rem;

      @include mixins.breakpoint(mobile) {
        gap: 0.75rem;
      }

      .galaxy-icon {
        position: relative;
        width: 44px;
        height: 44px;
        background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.125rem;
        box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
        flex-shrink: 0;

        @include mixins.breakpoint(mobile) {
          width: 40px;
          height: 40px;
          font-size: 1rem;
        }

        .orbit-ring {
          position: absolute;
          top: -6px;
          left: -6px;
          right: -6px;
          bottom: -6px;
          border: 1px solid rgba(139, 92, 246, 0.4);
          border-radius: 50%;
          animation: orbit-rotate 8s linear infinite;
        }
      }

      .journey-info {
        flex: 1;
        min-width: 0;

        .journey-title {
          font-size: 1rem;
          font-weight: 700;
          color: white;
          margin: 0 0 0.375rem;
          letter-spacing: -0.025em;
          line-height: 1.2;

          @include mixins.breakpoint(mobile) {
            font-size: 0.9375rem;
            margin-bottom: 0.25rem;
          }
        }

        .journey-details {
          display: flex;
          flex-direction: column;
          gap: 0.25rem;

          @include mixins.breakpoint(mobile) {
            gap: 0.125rem;
          }

          .free-lesson {
            display: flex;
            align-items: center;
            gap: 0.375rem;
            font-size: 1rem;
            font-weight: 600;
            color: #fbbf24;

            @include mixins.breakpoint(mobile) {
              font-size: 0.75rem;
            }

            i {
              font-size: 0.75rem;
              color: #fbbf24;
            }
          }

          .journey-status {
            font-size: 0.75rem;
            color: rgba(255, 255, 255, 0.8);
            font-weight: 500;

            @include mixins.breakpoint(mobile) {
              font-size: 0.6875rem;
            }
          }
        }
      }

      .launch-cta {
        flex-shrink: 0;

        .galaxy-launch-btn {
          background: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%) !important;
          border: 2px solid #8b5cf6 !important;
          color: white !important;
          font-weight: 600 !important;
          padding: 0.625rem 1.125rem !important;
          border-radius: 8px !important;
          transition: $transition-base !important;
          font-size: 0.75rem !important;
          box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3) !important;

          @include mixins.breakpoint(mobile) {
            padding: 0.5rem 0.875rem !important;
            font-size: 0.6875rem !important;
          }

          &:hover {
            background: linear-gradient(135deg, #7c3aed 0%, #9333ea 100%) !important;
            border-color: #7c3aed !important;
            transform: translateY(-2px) !important;
            box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4) !important;
          }

          &:active {
            transform: translateY(-1px) !important;
          }

          &:focus {
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3) !important;
          }

          .p-button-icon {
            color: white !important;
            font-size: 0.75rem !important;
            margin-left: 0.25rem !important;

            @include mixins.breakpoint(mobile) {
              font-size: 0.6875rem !important;
            }
          }

          .p-button-label {
            color: white !important;
            font-weight: 600 !important;
          }
        }
      }
    }
  }
}

.success-container {
  max-width: 640px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 2rem;

  @include mixins.breakpoint(mobile) {
    gap: 1.5rem;
  }

  // Success Header
  .success-header {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: $shadow-lg;
    border: 1px solid $gray-200;
    position: relative;
    overflow: hidden;

    @include mixins.breakpoint(mobile) {
      padding: 1.5rem;
      border-radius: 12px;
    }

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, $success 0%, $primary 100%);
    }

    .success-icon {
      position: relative;
      display: inline-block;
      margin-bottom: 1.5rem;

      @include mixins.breakpoint(mobile) {
        margin-bottom: 1.25rem;
      }

      .icon-background {
        width: 80px;
        height: 80px;
        background: linear-gradient(135deg, $success 0%, #34d399 100%);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 2rem;
        box-shadow: $shadow-lg;
        position: relative;
        z-index: 2;

        @include mixins.breakpoint(mobile) {
          width: 64px;
          height: 64px;
          font-size: 1.5rem;
        }
      }

      .success-glow {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 120px;
        height: 120px;
        background: radial-gradient(circle, rgba(16, 185, 129, 0.2) 0%, transparent 70%);
        border-radius: 50%;
        animation: success-pulse 3s ease-in-out infinite;
        z-index: 1;

        @include mixins.breakpoint(mobile) {
          width: 96px;
          height: 96px;
        }
      }
    }

    .success-content {
      .success-title {
        font-size: 2rem;
        font-weight: 700;
        color: $gray-900;
        margin: 0 0 0.75rem;
        letter-spacing: -0.025em;
        line-height: 1.2;

        @include mixins.breakpoint(mobile) {
          font-size: 1.75rem;
          margin-bottom: 0.5rem;
        }
      }

      .success-subtitle {
        font-size: 1.125rem;
        color: $gray-600;
        margin: 0;
        line-height: 1.5;

        @include mixins.breakpoint(mobile) {
          font-size: 1rem;
        }
      }
    }
  }

  // Trial Package Section
  .trial-package-section {
    background: white;
    border-radius: 16px;
    box-shadow: $shadow-md;
    border: 1px solid $gray-200;
    overflow: hidden;

    @include mixins.breakpoint(mobile) {
      border-radius: 12px;
    }

    .package-header {
      padding: 1.5rem;
      background: linear-gradient(135deg, $primary-50 0%, $orange-50 100%);
      border-bottom: 1px solid $gray-200;
      display: flex;
      align-items: center;
      gap: 1rem;

      @include mixins.breakpoint(mobile) {
        padding: 1.25rem;
        flex-direction: column;
        text-align: center;
        gap: 0.75rem;
      }

      .package-icon {
        width: 48px;
        height: 48px;
        background: linear-gradient(135deg, $orange 0%, #fb923c 100%);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.25rem;
        box-shadow: $shadow-sm;
        flex-shrink: 0;

        @include mixins.breakpoint(mobile) {
          width: 40px;
          height: 40px;
          font-size: 1.125rem;
        }
      }

      .package-content {
        flex: 1;

        .package-title {
          font-size: 1.5rem;
          font-weight: 700;
          color: $gray-900;
          margin: 0 0 0.25rem;
          letter-spacing: -0.025em;

          @include mixins.breakpoint(mobile) {
            font-size: 1.25rem;
            margin-bottom: 0.125rem;
          }
        }

        .package-subtitle {
          font-size: 0.875rem;
          color: $gray-600;
          margin: 0;
          line-height: 1.4;

          @include mixins.breakpoint(mobile) {
            font-size: 0.8125rem;
          }
        }
      }
    }

    .package-details {
      padding: 1.5rem;

      @include mixins.breakpoint(mobile) {
        padding: 1.25rem;
      }

      .package-card {
        .package-benefits {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 1.25rem;

          @include mixins.breakpoint(mobile) {
            grid-template-columns: 1fr;
            gap: 1rem;
          }

          .benefit-item {
            display: flex;
            align-items: flex-start;
            gap: 0.875rem;
            padding: 1rem;
            background: $gray-50;
            border-radius: 12px;
            border: 1px solid $gray-200;
            transition: $transition-base;

            @include mixins.breakpoint(mobile) {
              padding: 0.875rem;
              gap: 0.75rem;
            }

            &:hover {
              background: white;
              border-color: $primary-100;
              transform: translateY(-2px);
              box-shadow: $shadow-sm;
            }

            .benefit-icon {
              width: 36px;
              height: 36px;
              background: $primary-100;
              border-radius: 8px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: $primary;
              font-size: 1rem;
              flex-shrink: 0;

              @include mixins.breakpoint(mobile) {
                width: 32px;
                height: 32px;
                font-size: 0.875rem;
              }
            }

            .benefit-content {
              flex: 1;

              .benefit-title {
                font-size: 0.875rem;
                font-weight: 600;
                color: $gray-900;
                margin: 0 0 0.25rem;
                line-height: 1.3;

                @include mixins.breakpoint(mobile) {
                  font-size: 0.8125rem;
                }
              }

              .benefit-desc {
                font-size: 0.75rem;
                color: $gray-600;
                margin: 0;
                line-height: 1.4;

                @include mixins.breakpoint(mobile) {
                  font-size: 0.6875rem;
                }
              }
            }
          }
        }
      }
    }
  }

  // Next Steps Section
  .next-steps-section {
    text-align: center;
    padding: 2rem;
    background: white;
    border-radius: 16px;
    box-shadow: $shadow-md;
    border: 1px solid $gray-200;

    @include mixins.breakpoint(mobile) {
      padding: 1.5rem;
      border-radius: 12px;
    }

    .steps-title {
      font-size: 1.5rem;
      font-weight: 700;
      color: $gray-900;
      margin: 0 0 0.5rem;
      letter-spacing: -0.025em;

      @include mixins.breakpoint(mobile) {
        font-size: 1.25rem;
        margin-bottom: 0.375rem;
      }
    }

    .steps-subtitle {
      font-size: 0.875rem;
      color: $gray-600;
      margin: 0 0 2rem;
      line-height: 1.5;

      @include mixins.breakpoint(mobile) {
        font-size: 0.8125rem;
        margin-bottom: 1.5rem;
      }
    }

    .action-buttons {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;
      margin-bottom: 1.5rem;

      @include mixins.breakpoint(mobile) {
        flex-direction: column;
        gap: 0.875rem;
        margin-bottom: 1.25rem;
      }
    }

    .secondary-actions {
      display: flex;
      gap: 1rem;
      justify-content: center;
      flex-wrap: wrap;

      @include mixins.breakpoint(mobile) {
        flex-direction: column;
        gap: 0.875rem;
      }
    }
  }
}

// Animations
@keyframes success-pulse {
  0%, 100% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 1;
    transform: translate(-50%, -50%) scale(1.05);
  }
}

@keyframes twinkle {
  0%, 100% {
    opacity: 0.3;
    transform: scale(1);
  }
  50% {
    opacity: 1;
    transform: scale(1.2);
  }
}

@keyframes orbit-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// Reduced Motion Support
@media (prefers-reduced-motion: reduce) {
  .galaxy-journey-notification {
    .cosmic-background .star {
      animation: none;
      opacity: 0.5;
    }

    .galaxy-icon .orbit-ring {
      animation: none;
    }

    .journey-card:hover {
      transform: none !important;
    }

    .galaxy-launch-btn:hover {
      transform: none !important;
    }
  }
}
