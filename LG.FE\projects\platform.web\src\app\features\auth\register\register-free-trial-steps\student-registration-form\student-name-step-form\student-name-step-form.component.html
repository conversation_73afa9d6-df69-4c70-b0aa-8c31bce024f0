


<div  [@slideAnimation]="animationState()">
<form (ngSubmit)="submitStudentNameClicked($event)" [formGroup]="studentNameForm">
  <div class="text-center">
    <h3 class="primary-purple-color font-medium mb-3 mt-0">What is your student’s Name?</h3>

    <app-prime-reactive-form-input [inputClass]="'w-full'" [parentForm]="studentNameForm" [required]="true"
    label="Your Student's First name" [showLabelAbove]="true" 
      formControlName="firstName" placeholder="Enter your first name" type="text">
    </app-prime-reactive-form-input>
    <!-- <input type="text" class="w-full simple-form-input rounded mb-3" pInputText
        placeholder="Your Student's First name *" formControlName="firstName" /> -->
    <app-form-field-validation-message [control]="studentNameForm.controls['firstName']"
      messageClass="text-red-500" propertyName="First Name"></app-form-field-validation-message>


    <app-prime-reactive-form-input [inputClass]="'w-full'" [parentForm]="studentNameForm" [required]="true"
    label="Your Student's Last name" [showLabelAbove]="true" 
      formControlName="lastName" placeholder="Enter your last name" type="text">
    </app-prime-reactive-form-input>

    <app-form-field-validation-message [control]="studentNameForm.controls['lastName']" messageClass="text-red-500 mb-3"
      propertyName="Last Name"></app-form-field-validation-message>

    <div class="text-center mt-4">

      <p-button (click)="submitStudentNameClicked($event)" role="button"
      class=" gradient-purple-btn" styleClass="w-full" label="Save & Next"
     icon="pi pi-chevron-right" iconPos="right"></p-button>

    </div>
  </div>
</form>
</div>