import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, DestroyRef, inject, OnInit, signal, computed, Input, Output, EventEmitter } from '@angular/core';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { Router, NavigationEnd, ActivatedRoute } from '@angular/router';
import { filter } from 'rxjs/operators';
import { ButtonModule } from 'primeng/button';
import { TooltipModule } from 'primeng/tooltip';

export interface StepDefinition {
  id: number;
  route: string;
  title: string;
  shortTitle: string;
  description: string;
  icon: string;
}

export interface ProcessedStep extends StepDefinition {
  isCompleted: boolean;
  isActive: boolean;
  isAccessible: boolean;
}

export type NavigationCallback = (route: string) => void;

@Component({
  selector: 'app-dynamic-progress-steps',
  standalone: true,
  imports: [
    CommonModule,
    ButtonModule,
    TooltipModule
  ],
  templateUrl: './dynamic-progress-steps.component.html',
  styleUrl: './dynamic-progress-steps.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class DynamicProgressStepsComponent implements OnInit {
  // Injected services
  private readonly router = inject(Router);
  private readonly route = inject(ActivatedRoute);
  private readonly destroyRef = inject(DestroyRef);

  // Input properties
  @Input({ required: true }) stepDefinitions: StepDefinition[] = [];
  @Input() baseRoutePattern: string = '';
  @Input() flowTitle: string = 'Progress';
  @Input() completionRoute: string = '';
  @Input() navigationCallback?: NavigationCallback;
  @Input() disableNavigationOnCompletion: boolean = true;

  // Output events
  @Output() stepChanged = new EventEmitter<{ currentStep: ProcessedStep; stepIndex: number }>();
  @Output() navigationAttempted = new EventEmitter<{ targetStep: ProcessedStep; allowed: boolean }>();

  // Component state
  currentRoute = signal<string>('');
  currentStepIndex = signal<number>(0);

  // Computed properties
  steps = computed<ProcessedStep[]>(() => {
    const currentRoute = this.currentRoute();
    const currentIndex = this.currentStepIndex();
    const isFlowComplete = this.completionRoute && currentRoute === this.completionRoute;

    return this.stepDefinitions.map((step, index) => ({
      ...step,
      isActive: step.route === currentRoute,
      isCompleted: index < currentIndex,
      // If flow is complete and navigation is disabled, only the completion step should be accessible
      isAccessible: (isFlowComplete && this.disableNavigationOnCompletion) 
        ? step.route === this.completionRoute 
        : index <= currentIndex
    }));
  });

  progressPercentage = computed<number>(() => {
    const currentIndex = this.currentStepIndex();
    const totalSteps = this.stepDefinitions.length;
    return totalSteps > 1 ? Math.round((currentIndex / (totalSteps - 1)) * 100) : 100;
  });

  activeStep = computed<ProcessedStep | undefined>(() => {
    return this.steps().find(step => step.isActive);
  });

  ngOnInit(): void {
    // Initialize current route
    this.updateCurrentRoute();

    // Listen to route changes
    this.router.events
      .pipe(
        filter(event => event instanceof NavigationEnd),
        takeUntilDestroyed(this.destroyRef)
      )
      .subscribe(() => {
        this.updateCurrentRoute();
      });
  }

  /**
   * Updates the current route and step index based on the current URL
   */
  private updateCurrentRoute(): void {
    const url = this.router.url;
    let matchedStep: StepDefinition | undefined;

    if (this.baseRoutePattern) {
      // Use base route pattern for matching
      matchedStep = this.stepDefinitions.find(step => 
        url.includes(this.baseRoutePattern) && url.includes(step.route)
      );
    } else {
      // Fallback to simple route matching
      matchedStep = this.stepDefinitions.find(step => url.includes(step.route));
    }
    
    if (matchedStep) {
      this.currentRoute.set(matchedStep.route);
      this.currentStepIndex.set(matchedStep.id - 1);
      
      // Emit step change event
      const currentStep = this.steps().find(s => s.isActive);
      if (currentStep) {
        this.stepChanged.emit({ 
          currentStep, 
          stepIndex: this.currentStepIndex() 
        });
      }
    }
  }

  /**
   * Navigates to a specific step if it's accessible
   */
  navigateToStep(step: ProcessedStep): void {
    const isFlowComplete = this.completionRoute && this.currentRoute() === this.completionRoute;
    const navigationAllowed = step.isAccessible && !step.isActive && 
      !(isFlowComplete && this.disableNavigationOnCompletion);
    
    // Emit navigation attempt event
    this.navigationAttempted.emit({ targetStep: step, allowed: navigationAllowed });
    
    if (!navigationAllowed) {
      return;
    }

    // Use custom navigation callback if provided, otherwise use router
    if (this.navigationCallback) {
      this.navigationCallback(step.route);
    } else {
      this.router.navigate([step.route]);
    }
  }

  /**
   * Gets the CSS classes for a step indicator
   */
  getStepIndicatorClasses(step: ProcessedStep): string {
    const baseClasses = 'step-indicator flex align-items-center justify-content-center border-circle transition-all duration-200';
    const isFlowComplete = this.completionRoute && this.currentRoute() === this.completionRoute;
    
    if (step.isActive) {
      return `${baseClasses} step-active bg-primary text-white border-2 border-primary`;
    } else if (step.isCompleted) {
      // If flow is complete and navigation is disabled, show completed steps as disabled
      const interactionClasses = (isFlowComplete && this.disableNavigationOnCompletion) ? '' : 'cursor-pointer hover:bg-green-600';
      return `${baseClasses} step-completed bg-green-500 text-white border-2 border-green-500 ${interactionClasses}`;
    } else if (step.isAccessible && !(isFlowComplete && this.disableNavigationOnCompletion)) {
      return `${baseClasses} step-accessible bg-surface-100 text-600 border-2 border-300 cursor-pointer hover:bg-surface-200`;
    } else {
      return `${baseClasses} step-disabled bg-surface-50 text-400 border-2 border-200`;
    }
  }

  /**
   * Gets the CSS classes for a step title
   */
  getStepTitleClasses(step: ProcessedStep): string {
    const baseClasses = 'step-title text-sm font-medium transition-colors duration-200';
    const isFlowComplete = this.completionRoute && this.currentRoute() === this.completionRoute;
    
    if (step.isActive) {
      return `${baseClasses} text-primary`;
    } else if (step.isCompleted) {
      // If flow is complete and navigation is disabled, show completed steps as disabled
      const interactionClasses = (isFlowComplete && this.disableNavigationOnCompletion) ? '' : 'cursor-pointer hover:text-green-700';
      return `${baseClasses} text-green-600 ${interactionClasses}`;
    } else if (step.isAccessible && !(isFlowComplete && this.disableNavigationOnCompletion)) {
      return `${baseClasses} text-600 cursor-pointer hover:text-700`;
    } else {
      return `${baseClasses} text-400`;
    }
  }

  /**
   * Gets the tooltip text for a step
   */
  getStepTooltip(step: ProcessedStep): string {
    const isFlowComplete = this.completionRoute && this.currentRoute() === this.completionRoute;
    
    if (step.isActive) {
      return `Current step: ${step.description}`;
    } else if (step.isCompleted) {
      const canNavigate = !(isFlowComplete && this.disableNavigationOnCompletion);
      return canNavigate 
        ? `Completed: ${step.description} (Click to revisit)` 
        : `Completed: ${step.description}`;
    } else if (step.isAccessible) {
      return `Next: ${step.description}`;
    } else {
      return `Upcoming: ${step.description}`;
    }
  }
}
