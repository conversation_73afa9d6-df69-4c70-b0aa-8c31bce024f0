<div class="relative">

  <div class="relative form-wrapper  primary-purple-color">

    <div class="grid grid-nogutter">

      <div class="blob-circle-wrapper relative col-12 xl:col-3 z-2 left-0 top-0">
        <!-- <h3
          class="text-600 mb-1 mt-3 text-center xl:text-left sm:text-xl xl:text-5xl  relative z-3 md:pl-5">
          Request a Free Trial</h3> -->
      </div>

      <div
        class="col-12">


        <div class="flex align-items-center justify-content-center gap-3">


          <div class="gap-4 primary-purple-color px-3 py-2 md:px-4">
            <!-- <h2 class="text-center text-xl md:text-2xl font-medium mb-4">Why would you like to request a free trial?</h2> -->
            
            <div class="flex flex-column gap-3 w-full max-w-30rem mx-auto">
                <ng-container *ngFor="let item of reasons; let i = index">
                    <div (click)="selectItem(item.id)"
                        [ngClass]="{'selected-option': trialReasonType === item.id}"
                        class="option-card surface-card p-3 border-round-xl shadow-1 hover:shadow-3 transition-all transition-duration-200 cursor-pointer">
                        
                        <div class="flex align-items-center gap-3">
                            <div class="flex-shrink-0">
                                <p-radioButton
                                    [(ngModel)]="trialReasonType"
                                    [inputId]="'trialReasonType' + i"
                                    [name]="item.id"
                                    [value]="item.id"
                                    styleClass="reason-radio">
                                </p-radioButton>
                            </div>
                            
                            <label [for]="'trialReasonType' + i" 
                                   class="flex-grow-1 cursor-pointer text-primary font-bold">
                                {{ item.name }}
                                <p class="text-600 text-sm mt-1 mb-0" *ngIf="item.description">{{ item.description }}</p>
                            </label>
                        </div>
                    </div>
                </ng-container>
            </div>
        
            <div class="flex w-full justify-content-center mt-4">
                <p-button (click)="onSubmit()"
                          [disabled]="!trialReasonType"
                          [rounded]="true"
                          class="w-full max-w-30rem"
                          styleClass="p-3 text-lg w-full gradient-purple-btn"
                          icon="pi pi-arrow-right"
                          iconPos="right"
                          label="Continue">
                </p-button>
            </div>
        </div>
          <!-- <div class=" border-1 border-white p-4 border-round-xl">
              <p-checkbox [(ngModel)]="reason[0].id" binary="true" class="primary-availability-dropdown"
                  styleClass=" outlined" label="I want to learn a new language"></p-checkbox>
          </div>
          <div class=" border-1 border-white p-4 border-round-xl">
              <p-checkbox [(ngModel)]="reason[1].id" binary="true" class="primary-availability-dropdown"
                  styleClass=" outlined" label="I want to learn a new language"></p-checkbox>
          </div> -->
        </div>
      </div>

      <!-- <div class="absolute astro-man-bottom hidden xl:block">
        <img class="w-20rem xl:w-22rem" src="assets/images/freetrial-astronaut.webp"/>
      </div> -->

    </div>

    <!-- }@placeholder {
    <div class="flex align-items-center justify-content-center h-8rem">
        <span class="inline-loader">
            <span class="loader-box">
            </span><span class="loader-box"></span>
            <span class="loader-box"></span>
        </span>
    </div>
    } -->


  </div>
</div>
