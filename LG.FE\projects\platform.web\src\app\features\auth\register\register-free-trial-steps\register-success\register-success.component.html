<div class="flex flex-column align-items-center justify-content-center w-full h-full md:px-2">
  <div class="flex flex-column align-items-center justify-content-center w-full h-full primary-purple-color">
    <!-- <img alt="successfully registered" class="w-5rem my-2" src="assets/images/graphic/check.svg" /> -->
    <div class="mt-0 text-center">
      <!-- <h3 class="font-semibold mb-3 text-center m-0">Student registration successful!
      </h3> -->


      <!-- Galaxy Learning Journey Notification -->
      <div class="galaxy-journey-notification">
        <div class="journey-card">
          <div class="cosmic-background">
            <div class="star star-1"></div>
            <div class="star star-2"></div>
            <div class="star star-3"></div>
          </div>

          <div class="journey-content">
            <div class="galaxy-icon">
              <i class="pi pi-send"></i>
              <div class="orbit-ring"></div>
            </div>

            <div class="journey-info text-left">
              <h4 class="journey-title">Learning Journey Begins!</h4>
              <div class="journey-details">
                <span class="free-lesson">
                  <i class="pi pi-star-fill"></i>
                  1 Free {{teachingLanguageName()}} Lesson Ready
                </span>
                <span class="journey-status">Launch into Discovery</span>
              </div>
            </div>

          </div>
        </div>
      </div>

      <p class="text-800 text-sm mb-3"> Your student has been successfully registered. You can now access the student
        details to manage their progress.</p>



    </div>
  </div>


  <div class="mt-3 w-full">
    <app-available-students-for-groups-list [studentId]="studentId()!"
      [teachingLanguageId]="teachingLanguageId()!"></app-available-students-for-groups-list>
  </div>


  <div class="w-full mt-3">

    <h3 class="font-semibold mb-3 text-center m-0">Would you like to register another student?</h3>
    <div class="flex mt-2 gap-2 align-items-center justify-content-center">

      <p-button (click)="addAnotherStudent()" [rounded]="true" class=" " icon="pi pi-plus" iconPos="left"
        label="Add another Student" styleClass="cyan-btn"></p-button>
      <p-button [rounded]="true" [routerLink]="['/dashboard/parent/students']" class="" icon="pi pi-arrow-up-right"
        iconPos="left" label="Go to Students" styleClass="submit-btn"></p-button>
    </div>
  </div>

</div>