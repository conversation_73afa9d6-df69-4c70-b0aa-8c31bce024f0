import { CommonModule } from '@angular/common';
import { ChangeDetectionStrategy, Component, EventEmitter, inject, Injector, Input, input, model, Output, signal, ViewChild, type OnInit } from '@angular/core';
import { toObservable } from '@angular/core/rxjs-interop';
import { FormsModule } from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { MultiSelectModule } from 'primeng/multiselect';
import { Select, SelectModule } from 'primeng/select';
import { TooltipModule } from 'primeng/tooltip';
import { ISearchStudentDto, IStudentTeachingLanguageDto, PrimeProfilePhotoSingleComponent, untilDestroyed } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
type SelectionMode = 'single' | 'multiple';

@Component({
  selector: 'app-prime-students-selection',
  imports: [
    CommonModule,
    FormsModule,
    SelectModule,
    ButtonModule,
    TooltipModule,
    MultiSelectModule,
    PrimeProfilePhotoSingleComponent,
  ],
  templateUrl: './prime-students-selection.component.html',
  styleUrl: './prime-students-selection.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush,
})
export class PrimeStudentsSelectionComponent implements OnInit {
  @ViewChild ('selectStudentsList') select: Select | undefined;
  generalService = inject(GeneralService);
  @Input() selectionMode: SelectionMode = 'single';
  @Input() nameProperty: string = 'name'; // Default image property name
  @Input() imageProperty: string = 'image'; // Default image property name
  @Input() groupImageProperty: string = 'image'; // Default image property name
  @Input() imageProperties: string[] = []; // Array of image property names
  @Input() itemImage: string = '';
  @Input() languagesMode = false;
  @Input() studentGroupMode = false;
  @Input() studentGroup = [] as unknown[];
  @Input() createNewText!: string;
  @Input() createNewImage!: string;
  @Input() selectable = true;
  @Input() numVisible = 4;
  @Input() isItemSelected = false;
  @Input() selectedItem = ({} as any);
  @Input() emptyMessageText = 'No students found.';
  styleClass = input('w-full full-width mb-2');
  selectedItemProperty = input('');
  items = input([] as ISearchStudentDto[]);
  baseProperty = input(''); // Default image property name
  textForNameProperty = input('');
  resetSelectionSignal = input(false);
  private untilDestroyed = untilDestroyed();
  private injector = inject(Injector);
  @Output() itemClicked = new EventEmitter<ISearchStudentDto | ISearchStudentDto[]>();
  @Output() newItemClicked = new EventEmitter<void>();
  @Output() pageChanged = new EventEmitter<number>();

  selectedStudent = model({} as ISearchStudentDto);
  selectedStudents = model([] as ISearchStudentDto[]);

  // Language display constants
  private readonly MAX_VISIBLE_LANGUAGES = 3;

  ngOnInit(): void {
    console.log(this.items);

    if (this.selectedStudents().length > 0) {
      this.selectedStudents.set(this.items().filter(item => item.userId !== null && this.selectedStudents().some(selectedItem => selectedItem.userId === item.userId)))
    }
    toObservable(this.resetSelectionSignal, {
      injector: this.injector
    }).pipe(this.untilDestroyed()).subscribe({
      next: (reset) => {
        if (reset) {
          console.log('resetSelectionSignal', reset);
          this.select?.clear();
          (this.selectedStudent.set({} as ISearchStudentDto));
          this.itemClicked.emit(this.selectedStudent());
        }
      }
    });
  }

  onSelectionChange(event: any) {
    console.log(event);
    this.selectedStudent.set(event.value);
    this.itemClicked.emit(this.selectedStudent());
  }

  onSelectMultipleChange(event: any) {
    console.log(event);
    // this.selectedStudent.set(event.value);
    this.itemClicked.emit(event.value);
  }

  selectAllStudents() {
    this.selectedStudents.set(this.items());
    this.itemClicked.emit(this.items());
  }

  clearAllStudents() {
    this.selectedStudents.set([]);
    this.itemClicked.emit([]);
  }

  // Language display helper methods
  shouldTruncateLanguages(languages: IStudentTeachingLanguageDto[]): boolean {
    return languages && languages.length > this.MAX_VISIBLE_LANGUAGES;
  }

  getVisibleLanguages(languages: IStudentTeachingLanguageDto[]): IStudentTeachingLanguageDto[] {
    if (!languages) return [];
    return languages.slice(0, this.MAX_VISIBLE_LANGUAGES);
  }

  getRemainingLanguagesCount(languages: IStudentTeachingLanguageDto[]): number {
    if (!languages) return 0;
    return Math.max(0, languages.length - this.MAX_VISIBLE_LANGUAGES);
  }

  getAllLanguagesTooltip(languages: IStudentTeachingLanguageDto[]): string {
    if (!languages || languages.length === 0) return '';

    const languageTexts = languages.map(lang =>
      `${lang.teachingLanguageName} - ${this.generalService.getILanguageLevelsEnumText(lang.languageLevel)}`
    );

    return `All languages:\n${languageTexts.join('\n')}`;
  }

  getRemainingLanguagesTooltip(languages: IStudentTeachingLanguageDto[]): string {
    if (!languages || languages.length <= this.MAX_VISIBLE_LANGUAGES) return '';

    const remainingLanguages = languages.slice(this.MAX_VISIBLE_LANGUAGES);
    const languageTexts = remainingLanguages.map(lang =>
      `${lang.teachingLanguageName} - ${this.generalService.getILanguageLevelsEnumText(lang.languageLevel)}`
    );

    return `Additional languages:\n${languageTexts.join('\n')}`;
  }
}
