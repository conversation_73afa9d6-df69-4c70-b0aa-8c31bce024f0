import {
  IGenderEnum,
  IGetAllTeachingLanguagesResponse,
  IGetLanguagesResponse,
  ILanguageLevelsEnum,
  ISpeakingLanguageDto,
  IStudentLevelEnum,
  ITeachingLanguageDto,
  PrimeDateOfBirthPickerComponent
} from 'SharedModules.Library';
import { CommonModule, DatePipe } from '@angular/common';
import { Component, computed, DestroyRef, EventEmitter, inject, Output, Renderer2, signal, ViewChild } from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  FormsModule,
  ReactiveFormsModule,
  ValidationErrors,
  Validators
} from '@angular/forms';
import { ButtonModule } from 'primeng/button';
import { DropdownModule } from 'primeng/dropdown';
import { RadioButtonModule } from 'primeng/radiobutton';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { DatePicker, DatePickerModule, DatePickerTypeView, DatePickerYearChangeEvent } from 'primeng/datepicker';
import { FluidModule } from 'primeng/fluid';
import moment, { Moment } from 'moment-timezone';
import { MessageService } from 'primeng/api';
import { MessageModule } from 'primeng/message';
import { MessagesModule } from 'primeng/messages';
import { HandleApiResponseService } from 'SharedModules.Library';
import { AuthStateService } from 'SharedModules.Library';
import { GeneralService } from 'SharedModules.Library';
import { RegisterService } from '@platform.src/app/core/services/register.service';
import { FormFieldValidationMessageComponent } from 'SharedModules.Library';
import { StudentRegistrationProgressStepsComponent } from '../student-registration-progress-steps/student-registration-progress-steps.component';

@Component({
  selector: 'app-student-info-step-form',
  standalone: true, // Assuming you want to keep it standalone; adjust if it's part of a module
  imports: [
    CommonModule,
    FormsModule,
    ButtonModule,
    RadioButtonModule,
    DropdownModule,
    FluidModule,
    ReactiveFormsModule,
    MessageModule,
    MessagesModule,
    FormFieldValidationMessageComponent,
    PrimeDateOfBirthPickerComponent, // Replace DatePickerModule with the new component
    StudentRegistrationProgressStepsComponent
  ],
  templateUrl: './student-info-step-form.component.html',
  styleUrl: './student-info-step-form.component.scss',
  providers: [MessageService, DatePipe]
})
export class StudentInfoStepFormComponent {
  apiService = inject(HandleApiResponseService);
  generalService = inject(GeneralService);
  registerService = inject(RegisterService);
  authStateService = inject(AuthStateService);
  loadingLanguages = signal(true);
  languages!: string[];
  selectedLanguage!: string;
  ages: number[] = [];
  gender: IGenderEnum = IGenderEnum.Male;
  IGenderEnum: typeof IGenderEnum = IGenderEnum;
  IStudentLevelEnum = IStudentLevelEnum;
  levels = this.generalService.levels;
  @Output() submitStudentInfoFormButtonClick = new EventEmitter<any>();
  mltLanguages: ITeachingLanguageDto[] = [];
  formSubmitted = false;
  form: FormGroup = new FormGroup({});
  minDate: Date = new Date();
  maxDate: Date = new Date();
  defaultDate: Date = new Date();
  user = computed(() => {
    return this.authStateService.getUserClaims();
  });
  dateFormatToSubmit = 'YYYY-MM-DDT12:00:000[Z]'; // API format
  calendarView = 'year' as DatePickerTypeView;
  clendarTypeView: DatePickerTypeView = 'year';
  disabledPrev = signal(false);
  disabledNext = signal(false);
  selectedLanguageOfInterest = {} as ITeachingLanguageDto;
  private renderer = inject(Renderer2);
  private readonly destroy: DestroyRef = inject(DestroyRef);

  labelClass = 'block font-medium mt-3 mb-2 primary-purple-color text-900 text-left text-md w-full';

  constructor(private fb: FormBuilder) { }

  ngOnInit(): void {

    this.form = this.fb.group({
      gender: [this.registerService.getRegisterStudentRequest().gender, Validators.required],
      dateOfBirth: [this.getFormDateOfBirth(), Validators.required],
      nativeLanguage: [
        this.registerService.getRegisterStudentRequest().speakingLanguageDtos![0]?.language || null,
        Validators.required
      ],
      languageOfInterestId: [
        this.registerService.getRegisterStudentRequest().languageOfInterestId || null,
        Validators.required
      ],
      level: [this.registerService.getRegisterStudentRequest().studentLevel || null, Validators.required]
    });

    this.loadLanguages();
    this.loadTeachingLanguages();
  }

  ngAfterViewInit(): void { }

  ngOnChanges() {
    // this.setClickListeners();
  }

  submitInfoClicked(event: Event) {
    console.log(this.form);
    this.markControlsAsTouchedAndDirty();
    if (this.form.invalid) {
      return;
    }
    this.updateRegisterStudentRequest();
    this.registerService.navigateToNextStep('student-availability');
    this.emitSubmitStudentInfoFormButtonClick();
  }

  getFormDateOfBirth() {
    const formattedDate = this.registerService.getRegisterStudentRequest()?.dateOfBirth
      ? moment(this.registerService.getRegisterStudentRequest().dateOfBirth).format(this.dateFormatToSubmit)
      : null;
    return formattedDate;
  }
  markControlsAsTouchedAndDirty() {
    Object.values(this.form.controls).forEach(control => {
      control.markAsTouched();
      control.markAsDirty();
    });
  }

  updateRegisterStudentRequest() {
    const speakingLanguage: ISpeakingLanguageDto = {
      language: this.form.value.nativeLanguage,
      isNative: true,
      languageLevel: ILanguageLevelsEnum.None
    };
    this.registerService.updateRegisterStudentRequest({
      gender: this.form.value.gender,
      dateOfBirth: moment(this.form.value.dateOfBirth, this.dateFormatToSubmit).toDate(),
      speakingLanguageDtos: [speakingLanguage],
      languageName: this.selectedLanguageOfInterest.name,
      studentLevel: this.form.value.level,
      parentId: this.user()?.id
    });
    console.log(this.registerService.getRegisterStudentRequest());
  }

  emitSubmitStudentInfoFormButtonClick() {
    this.submitStudentInfoFormButtonClick.emit();
  }

  loadLanguages() {
    this.apiService.getLanguages().pipe(takeUntilDestroyed(this.destroy))
      .subscribe((data) => {
        console.log(data);
        this.processDialCodesData(data as IGetLanguagesResponse);
      });
  }

  loadTeachingLanguages() {
    this.apiService.getTeachingLanguages().pipe(takeUntilDestroyed(this.destroy))
      .subscribe((teachingLanguageResponse: IGetAllTeachingLanguagesResponse) => {
        console.log(teachingLanguageResponse);
        const languageOfInterest = this.findLanguageOfInterest(teachingLanguageResponse);
        if (languageOfInterest) {
          this.form.get('languageOfInterestId')?.setValue(languageOfInterest);
          this.selectedLanguageOfInterest = languageOfInterest;
        }
        this.mltLanguages = teachingLanguageResponse.teachingLanguages;
      });
  }

  onSelectedDateOfBirth(date: Date | string): void {
    console.log('Raw date input:', date);
    let parsedDate: Date;

    // If the provided value is a string, convert it to a Date.
    if (typeof date === 'string') {
      parsedDate = new Date(date);
    } else {
      parsedDate = date;
    }

    // Validate the parsed date.
    if (isNaN(parsedDate.getTime())) {
      console.error('Invalid date provided');
      return;
    }
    // Use moment to format the date according to the specified submission format.
    const formattedDate = moment(parsedDate).format(this.dateFormatToSubmit);
    // Set the formatted date to the form control.
    this.form.get('dateOfBirth')?.patchValue(formattedDate);
    console.log('Formatted date:', formattedDate);
  }

  isValidDateFromCalendarSelection(date: string): boolean {
    return moment(date, this.dateFormatToSubmit, true).isValid();
  }

  parseDate(dateString: string): Date {
    return moment(dateString, this.dateFormatToSubmit).toDate();
  }

  formatDateFromInput(date: string | Date): string {
    return moment(date).format(this.dateFormatToSubmit);
  }

  onLanguageOfInterestChange(event: { value: ITeachingLanguageDto}) {
    console.log(event);
    const selectedLang = event.value as ITeachingLanguageDto;
    this.selectedLanguageOfInterest = selectedLang;
    this.registerService.updateRegisterStudentRequest({
      ...this.registerService.getRegisterStudentRequest(),
      languageOfInterestId: this.selectedLanguageOfInterest.id,
      languageName: this.selectedLanguageOfInterest.name
    });
  }

  private findLanguageOfInterest(teachingLanguageResponse: IGetAllTeachingLanguagesResponse): ITeachingLanguageDto | undefined | null {
    const languageOfInterestId = this.registerService.getRegisterStudentRequest().languageOfInterestId;
    if (languageOfInterestId) {
      return teachingLanguageResponse.teachingLanguages.find((language: ITeachingLanguageDto) => language.id === languageOfInterestId);
    }
    return null;
  }

  private processDialCodesData(data: IGetLanguagesResponse) {
    this.loadingLanguages.set(false);
    this.languages = data.languages;
  }
}