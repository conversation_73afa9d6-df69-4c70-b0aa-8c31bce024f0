﻿// teacher-list-component-helper.service.ts
import {Injectable} from '@angular/core';
import { IEnumDropdownOptions } from '../models/enum-dropdown-options.model';
import {
  IAvailabilityStatusOptionsEnum, IGenderEnum, ITeacherAvailabilityStatusEnum,
  ITeacherStudentAgesExperienceEnum,
  ITeacherStudentAgesPreferenceEnum
} from '../GeneratedTsFiles';


@Injectable({
  providedIn: 'root'
})
export class EnumDropdownOptionsService {
  // this is the teacher availability status the same as its been saved in the database
  teacherAvailabilityStatusOptions: IEnumDropdownOptions[] = [
    {value: ITeacherAvailabilityStatusEnum.NotSet, label: 'Not Set'},
    {value: ITeacherAvailabilityStatusEnum.Open, label: 'Open'},
    {value: ITeacherAvailabilityStatusEnum.Full, label: 'Full'},
    {value: ITeacherAvailabilityStatusEnum.Limited, label: 'Limited'}    
  ];

  // this is the teacher availability statuses used only as multi-select filter
  availabilityStatusEnumFlagsOptions: IEnumDropdownOptions[] = [
    {label: 'Not Set', value: IAvailabilityStatusOptionsEnum.NotSet},
    {label: 'Open', value: IAvailabilityStatusOptionsEnum.Open},
    {label: 'Full', value: IAvailabilityStatusOptionsEnum.Full},
    {label: 'Limited', value: IAvailabilityStatusOptionsEnum.Limited},
    {label: 'Resigned', value: IAvailabilityStatusOptionsEnum.Resigned}
  ];

  genderOptions: IEnumDropdownOptions[] = [
    {label: 'All', value: IGenderEnum.None},
    {label: 'Male', value: IGenderEnum.Male},
    {label: 'Female', value: IGenderEnum.Female}
  ];

  teachingAgesExperienceEnumFlagsOptions: IEnumDropdownOptions[] = [
    {label: 'None', value: ITeacherStudentAgesExperienceEnum.None},
    {label: '2-4 Years', value: ITeacherStudentAgesExperienceEnum.TowToFour},
    {label: '4-6 Years', value: ITeacherStudentAgesExperienceEnum.FourToSix},
    {label: '6-8 Years', value: ITeacherStudentAgesExperienceEnum.SixToEight},
    {label: '8-10 Years', value: ITeacherStudentAgesExperienceEnum.EightToTen},
    {label: 'Above 10 Years', value: ITeacherStudentAgesExperienceEnum.AboveTen}
  ];

  teacherStudentAgesPreferenceEnumFlagsOptions: IEnumDropdownOptions[] = [
    {label: '2-4 Years', value: ITeacherStudentAgesPreferenceEnum.TowToFour},
    {label: '4-6 Years', value: ITeacherStudentAgesPreferenceEnum.FourToSix},
    {label: '6-8 Years', value: ITeacherStudentAgesPreferenceEnum.SixToEight},
    {label: '8-10 Years', value: ITeacherStudentAgesPreferenceEnum.EightToTen},
    {label: 'Above 10 Years', value: ITeacherStudentAgesPreferenceEnum.AboveTen}
  ];


  constructor() {
  }

  /**
   * Gets the label for a given value from a specified options array
   * @param optionsArray The array of IEnumDropdownOptions to search in
   * @param value The enum value to find the label for
   * @param defaultLabel Optional default label to return if value is not found
   * @returns The label corresponding to the value, or the defaultLabel if not found
   */
  getLabelFromValue<T>(optionsArray: IEnumDropdownOptions[], value: T, defaultLabel: string = ''): string {
    const option = optionsArray.find(opt => opt.value === value);
    return option ? option.label : defaultLabel;
  }

}
