<!-- Progress Steps Container -->
<div class="progress-steps-container w-full mb-4">
  <!-- Progress Header -->
  <div class="progress-header mb-3">
    <div class="flex align-items-center justify-content-between mb-2">
      <h3 class="text-lg font-semibold text-900 m-0">Student Registration</h3>
      <div class="progress-info text-sm text-600">
        Step {{ currentStepIndex() + 1 }} of {{ steps().length }}
      </div>
    </div>
    
    <!-- Progress Bar -->
    <div class="progress-bar-container relative">
      <div class="progress-bar-track w-full h-1 bg-surface-200 border-round-xl overflow-hidden">
        <div 
          class="progress-bar-fill h-full bg-primary transition-all duration-300 ease-in-out border-round-xl"
          [style.width.%]="progressPercentage()">
        </div>
      </div>
      <div class="progress-percentage text-xs text-600 mt-1 text-right">
        {{ progressPercentage() }}% Complete
      </div>
    </div>
  </div>

  <!-- Steps Navigation -->
  <div class="steps-navigation">
    <!-- Desktop View -->
    <div class="hidden md:block">
      <div class="flex align-items-start justify-content-between gap-2">
        <div 
          *ngFor="let step of steps(); let i = index; let isLast = last"
          class="step-item flex-1 flex flex-column align-items-center text-center"
          [class.cursor-pointer]="step.isAccessible && !step.isActive"
          (click)="navigateToStep(step)"
          [pTooltip]="getStepTooltip(step)"
          tooltipPosition="top">
          
          <!-- Step Indicator -->
          <div 
            [class]="getStepIndicatorClasses(step)"
            class="w-3rem h-3rem text-base font-semibold mb-2 relative">
            <i [class]="step.icon" *ngIf="!step.isCompleted"></i>
            <i class="pi pi-check" *ngIf="step.isCompleted"></i>
            
            <!-- Active Step Pulse Animation -->
            <div 
              *ngIf="step.isActive" 
              class="absolute inset-0 border-circle bg-primary opacity-25 animate-pulse">
            </div>
          </div>

          <!-- Step Title -->
          <div [class]="getStepTitleClasses(step)" class="mb-1">
            {{ step.shortTitle }}
          </div>

          <!-- Connection Line -->
          <div 
            *ngIf="!isLast" 
            class="step-connector absolute top-6 left-1/2 w-full h-px bg-surface-300 z-0"
            [class.bg-primary]="step.isCompleted"
            style="transform: translateX(50%); width: calc(100% - 1.5rem);">
          </div>
        </div>
      </div>
    </div>

    <!-- Mobile View -->
    <div class="block md:hidden">
      <div class="flex align-items-center gap-3 p-3 bg-surface-50 border-round-lg">
        <!-- Current Step Indicator -->
        <div [class]="getStepIndicatorClasses(activeStep()!)" class="w-2rem h-2rem text-sm">
          <i [class]="activeStep()?.icon" *ngIf="!activeStep()?.isCompleted"></i>
          <i class="pi pi-check" *ngIf="activeStep()?.isCompleted"></i>
        </div>

        <!-- Current Step Info -->
        <div class="flex-1">
          <div class="font-semibold text-900 text-sm mb-1">
            {{ activeStep()?.title }}
          </div>
          <div class="text-xs text-600">
            {{ activeStep()?.description }}
          </div>
        </div>

        <!-- Step Counter -->
        <div class="text-right">
          <div class="text-sm font-semibold text-primary">
            {{ currentStepIndex() + 1 }}/{{ steps().length }}
          </div>
        </div>
      </div>

      <!-- Mobile Steps Dots -->
      <div class="flex align-items-center justify-content-center gap-2 mt-3">
        <div 
          *ngFor="let step of steps()"
          class="step-dot w-2 h-2 border-circle transition-all duration-200"
          [class.bg-primary]="step.isActive"
          [class.bg-green-500]="step.isCompleted"
          [class.bg-surface-300]="!step.isActive && !step.isCompleted">
        </div>
      </div>
    </div>
  </div>

  <!-- Current Step Summary (Optional) -->
  <div class="current-step-summary mt-3 p-3 bg-primary-50 border-round-lg border-left-3 border-primary" *ngIf="activeStep()">
    <div class="flex align-items-center gap-2">
      <i [class]="activeStep()?.icon" class="text-primary"></i>
      <div>
        <div class="font-semibold text-primary text-sm">
          {{ activeStep()?.title }}
        </div>
        <div class="text-xs text-600 mt-1">
          {{ activeStep()?.description }}
        </div>
      </div>
    </div>
  </div>
</div>
